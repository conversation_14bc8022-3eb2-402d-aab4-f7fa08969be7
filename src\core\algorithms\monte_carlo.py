"""
Monte Carlo Simulation Engine for Options Pricing and Risk Analysis
Advanced Monte Carlo methods for options pricing, portfolio simulation, and risk assessment
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from scipy.stats import norm
import warnings

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


@dataclass
class MonteCarloParameters:
    """Parameters for Monte Carlo simulation."""
    initial_price: float
    strike_price: float
    time_to_expiration: float
    risk_free_rate: float
    volatility: float
    dividend_yield: float = 0.0
    n_simulations: int = 10000
    n_steps: int = 252
    option_type: str = "call"
    american_style: bool = False


@dataclass
class SimulationResult:
    """Result of Monte Carlo simulation."""
    option_price: float
    standard_error: float
    confidence_interval: Tuple[float, float]
    delta: float
    gamma: float
    theta: float
    vega: float
    probability_itm: float
    probability_profit: float
    price_paths: Optional[np.ndarray] = None


class MonteCarloEngine:
    """Advanced Monte Carlo simulation engine for options pricing."""
    
    def __init__(self, random_seed: Optional[int] = None):
        self.logger = logger
        if random_seed is not None:
            np.random.seed(random_seed)
    
    def price_option(
        self,
        S0: float,
        K: float,
        T: float,
        r: float,
        sigma: float,
        option_type: str = "call",
        n_simulations: int = 10000,
        n_steps: int = 252,
        dividend_yield: float = 0.0,
        american_style: bool = False
    ) -> Dict[str, Any]:
        """
        Price option using Monte Carlo simulation.
        
        Args:
            S0: Initial stock price
            K: Strike price
            T: Time to expiration (years)
            r: Risk-free rate
            sigma: Volatility
            option_type: 'call' or 'put'
            n_simulations: Number of simulation paths
            n_steps: Number of time steps
            dividend_yield: Dividend yield
            american_style: Whether option is American style
        
        Returns:
            Dictionary with pricing results
        """
        try:
            params = MonteCarloParameters(
                initial_price=S0,
                strike_price=K,
                time_to_expiration=T,
                risk_free_rate=r,
                volatility=sigma,
                dividend_yield=dividend_yield,
                n_simulations=n_simulations,
                n_steps=n_steps,
                option_type=option_type,
                american_style=american_style
            )
            
            if american_style:
                return self._price_american_option(params)
            else:
                return self._price_european_option(params)
                
        except Exception as e:
            self.logger.error(f"Monte Carlo pricing failed: {e}")
            return {"price": 0.0, "error": str(e)}
    
    def _price_european_option(self, params: MonteCarloParameters) -> Dict[str, Any]:
        """Price European option using Monte Carlo."""
        try:
            # Generate price paths
            price_paths = self._generate_price_paths(params)
            
            # Calculate payoffs at expiration
            final_prices = price_paths[:, -1]
            
            if params.option_type.lower() == "call":
                payoffs = np.maximum(final_prices - params.strike_price, 0)
            else:
                payoffs = np.maximum(params.strike_price - final_prices, 0)
            
            # Discount payoffs to present value
            option_price = np.exp(-params.risk_free_rate * params.time_to_expiration) * np.mean(payoffs)
            
            # Calculate standard error and confidence interval
            standard_error = np.std(payoffs) / np.sqrt(params.n_simulations)
            confidence_interval = (
                option_price - 1.96 * standard_error,
                option_price + 1.96 * standard_error
            )
            
            # Calculate Greeks using finite difference
            greeks = self._calculate_greeks_finite_diff(params)
            
            # Calculate probabilities
            prob_itm = np.mean(final_prices > params.strike_price) if params.option_type.lower() == "call" else np.mean(final_prices < params.strike_price)
            
            # Probability of profit (simplified - assumes buying option)
            breakeven = params.strike_price + option_price if params.option_type.lower() == "call" else params.strike_price - option_price
            prob_profit = np.mean(final_prices > breakeven) if params.option_type.lower() == "call" else np.mean(final_prices < breakeven)
            
            return {
                "price": option_price,
                "standard_error": standard_error,
                "confidence_interval": confidence_interval,
                "delta": greeks["delta"],
                "gamma": greeks["gamma"],
                "theta": greeks["theta"],
                "vega": greeks["vega"],
                "prob_itm": prob_itm,
                "prob_profit": prob_profit,
                "price_paths": price_paths
            }
            
        except Exception as e:
            self.logger.error(f"European option pricing failed: {e}")
            return {"price": 0.0, "error": str(e)}
    
    def _price_american_option(self, params: MonteCarloParameters) -> Dict[str, Any]:
        """Price American option using Longstaff-Schwartz method."""
        try:
            # Generate price paths
            price_paths = self._generate_price_paths(params)
            
            # Initialize cash flows matrix
            cash_flows = np.zeros_like(price_paths)
            
            # Set payoffs at expiration
            if params.option_type.lower() == "call":
                cash_flows[:, -1] = np.maximum(price_paths[:, -1] - params.strike_price, 0)
            else:
                cash_flows[:, -1] = np.maximum(params.strike_price - price_paths[:, -1], 0)
            
            # Backward induction using regression
            dt = params.time_to_expiration / params.n_steps
            
            for t in range(params.n_steps - 1, 0, -1):
                # Discount future cash flows
                discount_factor = np.exp(-params.risk_free_rate * dt)
                cash_flows[:, t] = cash_flows[:, t + 1] * discount_factor
                
                # Calculate intrinsic value
                if params.option_type.lower() == "call":
                    intrinsic = np.maximum(price_paths[:, t] - params.strike_price, 0)
                else:
                    intrinsic = np.maximum(params.strike_price - price_paths[:, t], 0)
                
                # Find in-the-money paths
                itm_mask = intrinsic > 0
                
                if np.sum(itm_mask) > 0:
                    # Regression to estimate continuation value
                    X = price_paths[itm_mask, t]
                    Y = cash_flows[itm_mask, t]
                    
                    # Polynomial regression (degree 2)
                    if len(X) > 3:
                        coeffs = np.polyfit(X, Y, min(2, len(X) - 1))
                        continuation_value = np.polyval(coeffs, X)
                        
                        # Exercise if intrinsic value > continuation value
                        exercise_mask = intrinsic[itm_mask] > continuation_value
                        
                        # Update cash flows for exercised options
                        itm_indices = np.where(itm_mask)[0]
                        exercise_indices = itm_indices[exercise_mask]
                        
                        cash_flows[exercise_indices, t] = intrinsic[exercise_indices]
                        cash_flows[exercise_indices, t + 1:] = 0  # No future cash flows after exercise
            
            # Calculate option price
            option_price = np.mean(cash_flows[:, 1] * np.exp(-params.risk_free_rate * dt))
            
            # Calculate other metrics (simplified for American options)
            standard_error = np.std(cash_flows[:, 1]) / np.sqrt(params.n_simulations)
            confidence_interval = (
                option_price - 1.96 * standard_error,
                option_price + 1.96 * standard_error
            )
            
            # Calculate Greeks (simplified)
            greeks = self._calculate_greeks_finite_diff(params)
            
            final_prices = price_paths[:, -1]
            prob_itm = np.mean(final_prices > params.strike_price) if params.option_type.lower() == "call" else np.mean(final_prices < params.strike_price)
            
            return {
                "price": option_price,
                "standard_error": standard_error,
                "confidence_interval": confidence_interval,
                "delta": greeks["delta"],
                "gamma": greeks["gamma"],
                "theta": greeks["theta"],
                "vega": greeks["vega"],
                "prob_itm": prob_itm,
                "prob_profit": prob_itm * 0.8,  # Simplified
                "price_paths": price_paths
            }
            
        except Exception as e:
            self.logger.error(f"American option pricing failed: {e}")
            return {"price": 0.0, "error": str(e)}
    
    def _generate_price_paths(self, params: MonteCarloParameters) -> np.ndarray:
        """Generate stock price paths using geometric Brownian motion."""
        dt = params.time_to_expiration / params.n_steps
        
        # Generate random shocks
        random_shocks = np.random.normal(0, 1, (params.n_simulations, params.n_steps))
        
        # Initialize price paths
        price_paths = np.zeros((params.n_simulations, params.n_steps + 1))
        price_paths[:, 0] = params.initial_price
        
        # Generate paths using GBM
        for t in range(1, params.n_steps + 1):
            drift = (params.risk_free_rate - params.dividend_yield - 0.5 * params.volatility**2) * dt
            diffusion = params.volatility * np.sqrt(dt) * random_shocks[:, t - 1]
            price_paths[:, t] = price_paths[:, t - 1] * np.exp(drift + diffusion)
        
        return price_paths
    
    def _calculate_greeks_finite_diff(self, params: MonteCarloParameters) -> Dict[str, float]:
        """Calculate Greeks using finite difference method."""
        try:
            # Base price
            base_result = self._price_european_option(params)
            base_price = base_result["price"]
            
            # Delta calculation
            h_spot = 0.01 * params.initial_price
            params_up = MonteCarloParameters(**{**params.__dict__, 'initial_price': params.initial_price + h_spot})
            params_down = MonteCarloParameters(**{**params.__dict__, 'initial_price': params.initial_price - h_spot})
            
            price_up = self._price_european_option(params_up)["price"]
            price_down = self._price_european_option(params_down)["price"]
            delta = (price_up - price_down) / (2 * h_spot)
            
            # Gamma calculation
            gamma = (price_up - 2 * base_price + price_down) / (h_spot**2)
            
            # Theta calculation
            h_time = 1.0 / 365.0  # One day
            if params.time_to_expiration > h_time:
                params_theta = MonteCarloParameters(**{**params.__dict__, 'time_to_expiration': params.time_to_expiration - h_time})
                price_theta = self._price_european_option(params_theta)["price"]
                theta = price_theta - base_price
            else:
                theta = 0.0
            
            # Vega calculation
            h_vol = 0.01  # 1% volatility change
            params_vega_up = MonteCarloParameters(**{**params.__dict__, 'volatility': params.volatility + h_vol})
            params_vega_down = MonteCarloParameters(**{**params.__dict__, 'volatility': params.volatility - h_vol})
            
            price_vega_up = self._price_european_option(params_vega_up)["price"]
            price_vega_down = self._price_european_option(params_vega_down)["price"]
            vega = (price_vega_up - price_vega_down) / (2 * h_vol)
            
            return {
                "delta": delta,
                "gamma": gamma,
                "theta": theta,
                "vega": vega
            }
            
        except Exception as e:
            self.logger.error(f"Greeks calculation failed: {e}")
            return {"delta": 0.0, "gamma": 0.0, "theta": 0.0, "vega": 0.0}
    
    def simulate_portfolio_returns(
        self,
        initial_values: List[float],
        correlations: np.ndarray,
        volatilities: List[float],
        time_horizon: float,
        n_simulations: int = 10000,
        n_steps: int = 252
    ) -> np.ndarray:
        """Simulate correlated portfolio returns."""
        try:
            n_assets = len(initial_values)
            dt = time_horizon / n_steps
            
            # Generate correlated random shocks
            random_shocks = np.random.multivariate_normal(
                mean=np.zeros(n_assets),
                cov=correlations,
                size=(n_simulations, n_steps)
            )
            
            # Initialize portfolio values
            portfolio_values = np.zeros((n_simulations, n_steps + 1))
            portfolio_values[:, 0] = np.sum(initial_values)
            
            # Simulate portfolio evolution
            for t in range(1, n_steps + 1):
                returns = np.zeros((n_simulations, n_assets))
                
                for i in range(n_assets):
                    drift = -0.5 * volatilities[i]**2 * dt
                    diffusion = volatilities[i] * np.sqrt(dt) * random_shocks[:, t - 1, i]
                    returns[:, i] = drift + diffusion
                
                # Calculate portfolio returns
                weights = np.array(initial_values) / np.sum(initial_values)
                portfolio_returns = np.sum(returns * weights, axis=1)
                portfolio_values[:, t] = portfolio_values[:, t - 1] * np.exp(portfolio_returns)
            
            return portfolio_values
            
        except Exception as e:
            self.logger.error(f"Portfolio simulation failed: {e}")
            return np.zeros((n_simulations, n_steps + 1))


# Global Monte Carlo engine instance
monte_carlo_engine = MonteCarloEngine()
