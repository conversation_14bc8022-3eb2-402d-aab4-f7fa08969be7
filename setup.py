"""
QuantEdgeFlow Setup Configuration
Package installation and distribution setup
"""

from setuptools import setup, find_packages
import os
from pathlib import Path

# Read README for long description
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    with open(requirements_path, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

# Version
VERSION = "1.0.0"

setup(
    name="quantedgeflow",
    version=VERSION,
    author="HectorTa1989",
    author_email="<EMAIL>",
    description="Elite Options Trading Analysis Platform",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/HectorTa1989/quantedgeflow",
    project_urls={
        "Bug Tracker": "https://github.com/HectorTa1989/quantedgeflow/issues",
        "Documentation": "https://github.com/HectorTa1989/quantedgeflow/blob/main/README.md",
        "Source Code": "https://github.com/HectorTa1989/quantedgeflow",
    },
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Framework :: FastAPI",
        "Topic :: Scientific/Engineering :: Mathematics",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "black>=23.11.0",
            "flake8>=6.1.0",
            "mypy>=1.7.1",
            "pre-commit>=3.5.0",
            "pytest-mock>=3.12.0",
            "factory-boy>=3.3.0",
            "faker>=20.1.0",
        ],
        "docs": [
            "mkdocs>=1.5.3",
            "mkdocs-material>=9.4.8",
            "mkdocstrings[python]>=0.24.0",
        ],
        "monitoring": [
            "prometheus-client>=0.19.0",
            "sentry-sdk>=1.38.0",
            "structlog>=23.2.0",
        ],
        "ml": [
            "tensorflow>=2.15.0",
            "torch>=2.1.2",
            "xgboost>=2.0.2",
            "lightgbm>=4.1.0",
            "catboost>=1.2.2",
        ],
    },
    entry_points={
        "console_scripts": [
            "quantedgeflow=src.main:main",
            "qef-migrate=src.scripts.migrate:main",
            "qef-backtest=src.scripts.backtest:main",
        ],
    },
    include_package_data=True,
    package_data={
        "quantedgeflow": [
            "config/*.yaml",
            "config/*.json",
            "templates/*.html",
            "static/*",
        ],
    },
    zip_safe=False,
    keywords=[
        "options trading",
        "quantitative finance",
        "portfolio management",
        "risk management",
        "financial analysis",
        "algorithmic trading",
        "derivatives",
        "black-scholes",
        "greeks",
        "volatility",
        "fastapi",
        "async",
        "real-time",
    ],
    license="MIT",
    platforms=["any"],
)
