"""
Options Chain Collector for QuantEdgeFlow
Specialized collector for options data, Greeks, and volatility metrics
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from decimal import Decimal
import yfinance as yf
import numpy as np
from scipy.stats import norm
import math

from config.database import get_async_session
from core.models.options import (
    OptionContract, OptionChain, VolatilitySurface, OptionType
)
from core.models.market_data import StockQuote
from core.algorithms.black_scholes import BlackScholesCalculator, OptionParameters
from utils.logger import get_performance_logger


logger = logging.getLogger(__name__)
performance_logger = get_performance_logger()


class OptionsChainCollector:
    """Specialized options data collector."""
    
    def __init__(self):
        self.logger = logger
        self.bs_calculator = BlackScholesCalculator()
        self.risk_free_rate = 0.05  # Default 5% risk-free rate
    
    async def collect_options_chain(self, symbol: str) -> bool:
        """Collect complete options chain for a symbol."""
        try:
            start_time = datetime.utcnow()
            
            # Get underlying stock data
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info or 'regularMarketPrice' not in info:
                self.logger.warning(f"No underlying data for {symbol}")
                return False
            
            underlying_price = Decimal(str(info['regularMarketPrice']))
            
            # Get available expiration dates
            expirations = ticker.options
            if not expirations:
                self.logger.warning(f"No options available for {symbol}")
                return False
            
            total_contracts = 0
            
            # Process each expiration
            for expiration_str in expirations:
                try:
                    expiration_date = datetime.strptime(expiration_str, '%Y-%m-%d').date()
                    
                    # Skip expired options
                    if expiration_date <= date.today():
                        continue
                    
                    # Get option chain for this expiration
                    option_chain = ticker.option_chain(expiration_str)
                    
                    # Process calls and puts
                    contracts = []
                    
                    # Process calls
                    for _, call_data in option_chain.calls.iterrows():
                        contract = await self._create_option_contract(
                            symbol, call_data, OptionType.CALL, 
                            expiration_date, underlying_price
                        )
                        if contract:
                            contracts.append(contract)
                    
                    # Process puts
                    for _, put_data in option_chain.puts.iterrows():
                        contract = await self._create_option_contract(
                            symbol, put_data, OptionType.PUT, 
                            expiration_date, underlying_price
                        )
                        if contract:
                            contracts.append(contract)
                    
                    if contracts:
                        # Store contracts in database
                        await self._store_option_contracts(contracts)
                        
                        # Create or update option chain record
                        await self._create_option_chain_record(
                            symbol, expiration_date, contracts, underlying_price
                        )
                        
                        total_contracts += len(contracts)
                    
                except Exception as e:
                    self.logger.error(f"Failed to process expiration {expiration_str} for {symbol}: {e}")
                    continue
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.info(f"Options chain collected", extra={
                "symbol": symbol,
                "expirations_processed": len(expirations),
                "total_contracts": total_contracts,
                "duration_seconds": duration
            })
            
            return total_contracts > 0
            
        except Exception as e:
            self.logger.error(f"Failed to collect options chain for {symbol}: {e}")
            return False
    
    async def _create_option_contract(
        self,
        symbol: str,
        option_data: Any,
        option_type: OptionType,
        expiration_date: date,
        underlying_price: Decimal
    ) -> Optional[OptionContract]:
        """Create option contract from raw data."""
        try:
            strike_price = Decimal(str(option_data['strike']))
            
            # Calculate days to expiration
            days_to_expiration = (expiration_date - date.today()).days
            
            # Create option symbol (OCC format)
            option_symbol = self._create_option_symbol(
                symbol, expiration_date, option_type, strike_price
            )
            
            # Get pricing data
            bid = Decimal(str(option_data.get('bid', 0))) if option_data.get('bid') else None
            ask = Decimal(str(option_data.get('ask', 0))) if option_data.get('ask') else None
            last_price = Decimal(str(option_data.get('lastPrice', 0))) if option_data.get('lastPrice') else None
            
            # Calculate mark price (mid-point)
            mark_price = None
            if bid and ask and bid > 0 and ask > 0:
                mark_price = (bid + ask) / 2
            elif last_price and last_price > 0:
                mark_price = last_price
            
            # Get volume and open interest
            volume = int(option_data.get('volume', 0))
            open_interest = int(option_data.get('openInterest', 0))
            
            # Get implied volatility
            implied_volatility = option_data.get('impliedVolatility')
            if implied_volatility and implied_volatility > 0:
                implied_volatility = float(implied_volatility)
            else:
                implied_volatility = None
            
            # Calculate Greeks if we have enough data
            delta = gamma = theta = vega = rho = None
            
            if mark_price and implied_volatility and days_to_expiration > 0:
                try:
                    greeks = await self._calculate_greeks(
                        underlying_price, strike_price, days_to_expiration,
                        implied_volatility, option_type
                    )
                    if greeks:
                        delta = greeks['delta']
                        gamma = greeks['gamma']
                        theta = greeks['theta']
                        vega = greeks['vega']
                        rho = greeks['rho']
                except Exception as e:
                    self.logger.debug(f"Failed to calculate Greeks: {e}")
            
            # Calculate intrinsic and time value
            intrinsic_value = self._calculate_intrinsic_value(
                underlying_price, strike_price, option_type
            )
            
            time_value = None
            if mark_price:
                time_value = max(Decimal(0), mark_price - intrinsic_value)
            
            # Calculate moneyness
            moneyness = self._calculate_moneyness(
                underlying_price, strike_price, option_type
            )
            
            # Create contract
            contract = OptionContract(
                symbol=symbol.upper(),
                option_symbol=option_symbol,
                option_type=option_type,
                strike_price=strike_price,
                expiration_date=expiration_date,
                days_to_expiration=days_to_expiration,
                bid=bid,
                ask=ask,
                last_price=last_price,
                mark_price=mark_price,
                volume=volume,
                open_interest=open_interest,
                delta=delta,
                gamma=gamma,
                theta=theta,
                vega=vega,
                rho=rho,
                implied_volatility=implied_volatility,
                intrinsic_value=intrinsic_value,
                time_value=time_value,
                moneyness=moneyness,
                quote_time=datetime.utcnow()
            )
            
            return contract
            
        except Exception as e:
            self.logger.error(f"Failed to create option contract: {e}")
            return None
    
    async def _calculate_greeks(
        self,
        underlying_price: Decimal,
        strike_price: Decimal,
        days_to_expiration: int,
        implied_volatility: float,
        option_type: OptionType
    ) -> Optional[Dict[str, float]]:
        """Calculate option Greeks using Black-Scholes."""
        try:
            time_to_expiration = days_to_expiration / 365.0
            
            if time_to_expiration <= 0:
                return None
            
            params = OptionParameters(
                spot_price=float(underlying_price),
                strike_price=float(strike_price),
                time_to_expiration=time_to_expiration,
                risk_free_rate=self.risk_free_rate,
                volatility=implied_volatility,
                dividend_yield=0.0,  # Assume no dividends for now
                option_type=option_type
            )
            
            pricing = self.bs_calculator.calculate_full_pricing(params)
            
            return {
                'delta': pricing.delta,
                'gamma': pricing.gamma,
                'theta': pricing.theta,
                'vega': pricing.vega,
                'rho': pricing.rho
            }
            
        except Exception as e:
            self.logger.debug(f"Failed to calculate Greeks: {e}")
            return None
    
    def _calculate_intrinsic_value(
        self,
        underlying_price: Decimal,
        strike_price: Decimal,
        option_type: OptionType
    ) -> Decimal:
        """Calculate intrinsic value of option."""
        if option_type == OptionType.CALL:
            return max(Decimal(0), underlying_price - strike_price)
        else:  # PUT
            return max(Decimal(0), strike_price - underlying_price)
    
    def _calculate_moneyness(
        self,
        underlying_price: Decimal,
        strike_price: Decimal,
        option_type: OptionType
    ) -> float:
        """Calculate moneyness ratio."""
        if option_type == OptionType.CALL:
            return float(underlying_price / strike_price)
        else:  # PUT
            return float(strike_price / underlying_price)
    
    def _create_option_symbol(
        self,
        symbol: str,
        expiration_date: date,
        option_type: OptionType,
        strike_price: Decimal
    ) -> str:
        """Create standardized option symbol (OCC format)."""
        # Format: SYMBOL + YYMMDD + C/P + STRIKE (8 digits)
        exp_str = expiration_date.strftime('%y%m%d')
        type_char = 'C' if option_type == OptionType.CALL else 'P'
        strike_str = f"{int(strike_price * 1000):08d}"
        
        return f"{symbol.upper()}{exp_str}{type_char}{strike_str}"
    
    async def _store_option_contracts(self, contracts: List[OptionContract]) -> None:
        """Store option contracts in database."""
        async with get_async_session() as session:
            try:
                # Remove existing contracts for same symbol/expiration
                if contracts:
                    first_contract = contracts[0]
                    from sqlalchemy import delete
                    
                    delete_stmt = delete(OptionContract).where(
                        (OptionContract.symbol == first_contract.symbol) &
                        (OptionContract.expiration_date == first_contract.expiration_date)
                    )
                    await session.execute(delete_stmt)
                
                # Add new contracts
                session.add_all(contracts)
                await session.commit()
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to store option contracts: {e}")
    
    async def _create_option_chain_record(
        self,
        symbol: str,
        expiration_date: date,
        contracts: List[OptionContract],
        underlying_price: Decimal
    ) -> None:
        """Create or update option chain record."""
        async with get_async_session() as session:
            try:
                # Calculate chain statistics
                calls = [c for c in contracts if c.option_type == OptionType.CALL]
                puts = [c for c in contracts if c.option_type == OptionType.PUT]
                
                total_call_volume = sum(c.volume for c in calls)
                total_put_volume = sum(c.volume for c in puts)
                total_call_open_interest = sum(c.open_interest for c in calls)
                total_put_open_interest = sum(c.open_interest for c in puts)
                
                put_call_ratio = (total_put_volume / total_call_volume) if total_call_volume > 0 else 0
                
                # Calculate max pain (simplified)
                max_pain = await self._calculate_max_pain(contracts, underlying_price)
                
                # Create or update chain record
                from sqlalchemy import select
                
                stmt = select(OptionChain).where(
                    (OptionChain.symbol == symbol.upper()) &
                    (OptionChain.expiration_date == expiration_date)
                )
                
                result = await session.execute(stmt)
                chain = result.scalar_one_or_none()
                
                if not chain:
                    chain = OptionChain(
                        symbol=symbol.upper(),
                        expiration_date=expiration_date
                    )
                    session.add(chain)
                
                # Update chain data
                chain.underlying_price = underlying_price
                chain.total_call_volume = total_call_volume
                chain.total_put_volume = total_put_volume
                chain.total_call_open_interest = total_call_open_interest
                chain.total_put_open_interest = total_put_open_interest
                chain.put_call_ratio = put_call_ratio
                chain.max_pain = max_pain
                chain.updated_at = datetime.utcnow()
                
                await session.commit()
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to create option chain record: {e}")
    
    async def _calculate_max_pain(
        self,
        contracts: List[OptionContract],
        underlying_price: Decimal
    ) -> Optional[Decimal]:
        """Calculate max pain price (simplified calculation)."""
        try:
            # Get unique strike prices
            strikes = list(set(c.strike_price for c in contracts))
            strikes.sort()
            
            if not strikes:
                return None
            
            max_pain_price = None
            min_total_value = float('inf')
            
            # Calculate total option value at each strike
            for strike in strikes:
                total_value = 0
                
                for contract in contracts:
                    if contract.open_interest > 0:
                        # Calculate intrinsic value at this strike price
                        intrinsic = self._calculate_intrinsic_value(
                            strike, contract.strike_price, contract.option_type
                        )
                        total_value += float(intrinsic) * contract.open_interest
                
                if total_value < min_total_value:
                    min_total_value = total_value
                    max_pain_price = strike
            
            return max_pain_price
            
        except Exception as e:
            self.logger.debug(f"Failed to calculate max pain: {e}")
            return None


# Global options chain collector instance
options_chain_collector = OptionsChainCollector()
