# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database & Storage
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis==5.0.1
asyncpg==0.29.0

# Data Processing & Analytics
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4
scikit-learn==1.3.2
ta-lib==0.4.28
yfinance==0.2.28
alpha-vantage==2.3.1

# Options Pricing & Risk
py_vollib==1.0.1
quantlib==1.32
mibian==0.1.3

# Machine Learning
xgboost==2.0.2
lightgbm==4.1.0
catboost==1.2.2
tensorflow==2.15.0
torch==2.1.2

# Time Series & Financial Data
ccxt==4.1.64
websocket-client==1.6.4
python-binance==1.0.19
polygon-api-client==1.12.3

# Web & API
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0
websockets==12.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Monitoring & Logging
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk==1.38.0

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1
factory-boy==3.3.0
faker==20.1.0

# Text Processing
textblob==0.17.1
nltk==3.8.1

# Additional File Formats
openpyxl==3.1.2
xlsxwriter==3.1.9

# Rate Limiting
slowapi==0.1.9

# Environment & Configuration
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0

# Data Visualization
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0

# Async & Concurrency
asyncio==3.4.3
aiofiles==23.2.1
celery==5.3.4

# Utilities
python-dateutil==2.8.2
pytz==2023.3
schedule==1.2.0
cachetools==5.3.2
