{"timestamp": "2025-07-16T15:29:09.753289", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:10.526033", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:16.717692", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:17.376028", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:29:24.498226", "level": "ERROR", "logger": "src.main", "message": "Unhandled exception: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given", "module": "main", "function": "global_exception_handler", "line": 160, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py\", line 164, in __call__\n    await self.app(scope, receive, _send)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: RateLimitMiddleware.__call__() takes 3 positional arguments but 4 were given"}
{"timestamp": "2025-07-16T15:30:48.856620", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: Invalid argument(s) 'pool_size','max_overflow' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "module": "database", "function": "init_database", "line": 196}
{"timestamp": "2025-07-16T15:31:15.009513", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: Invalid argument(s) 'pool_size','max_overflow' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "module": "database", "function": "init_database", "line": 196}
{"timestamp": "2025-07-16T15:32:05.731862", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: Invalid argument(s) 'pool_size','max_overflow' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/NullPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "module": "database", "function": "init_database", "line": 212}
{"timestamp": "2025-07-16T15:32:45.325485", "level": "ERROR", "logger": "src.config.database", "message": "Database initialization failed: (sqlite3.OperationalError) near \"SET\": syntax error\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "database", "function": "init_database", "line": 230}
