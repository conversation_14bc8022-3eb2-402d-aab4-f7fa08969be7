"""
Volatility Models for QuantEdgeFlow
Advanced volatility modeling including GARCH, stochastic volatility, and implied volatility analysis
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from scipy.optimize import minimize
from scipy.stats import norm
import warnings

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


@dataclass
class VolatilityForecast:
    """Volatility forecast result."""
    forecast_values: np.ndarray
    confidence_intervals: List[Tuple[float, float]]
    model_type: str
    forecast_horizon: int
    accuracy_metrics: Dict[str, float]


@dataclass
class GARCHParameters:
    """GARCH model parameters."""
    omega: float  # Constant term
    alpha: float  # ARCH coefficient
    beta: float   # GARCH coefficient
    log_likelihood: float
    aic: float
    bic: float


class VolatilityCalculator:
    """Advanced volatility modeling and forecasting engine."""
    
    def __init__(self):
        self.logger = logger
        self.trading_days_per_year = 252
    
    def calculate_historical_volatility(
        self,
        prices: pd.Series,
        window: int = 30,
        annualized: bool = True
    ) -> pd.Series:
        """Calculate rolling historical volatility."""
        try:
            returns = prices.pct_change().dropna()
            
            if len(returns) < window:
                return pd.Series(dtype=float)
            
            volatility = returns.rolling(window=window).std()
            
            if annualized:
                volatility *= np.sqrt(self.trading_days_per_year)
            
            return volatility
            
        except Exception as e:
            self.logger.error(f"Historical volatility calculation failed: {e}")
            return pd.Series(dtype=float)
    
    def calculate_realized_volatility(
        self,
        prices: pd.Series,
        frequency: str = "daily"
    ) -> float:
        """Calculate realized volatility using high-frequency data."""
        try:
            returns = prices.pct_change().dropna()
            
            if len(returns) == 0:
                return 0.0
            
            # Calculate realized volatility
            realized_vol = np.sqrt(np.sum(returns**2))
            
            # Annualize based on frequency
            if frequency == "daily":
                realized_vol *= np.sqrt(self.trading_days_per_year)
            elif frequency == "hourly":
                realized_vol *= np.sqrt(self.trading_days_per_year * 24)
            elif frequency == "minute":
                realized_vol *= np.sqrt(self.trading_days_per_year * 24 * 60)
            
            return realized_vol
            
        except Exception as e:
            self.logger.error(f"Realized volatility calculation failed: {e}")
            return 0.0
    
    def fit_garch_model(
        self,
        returns: pd.Series,
        p: int = 1,
        q: int = 1
    ) -> GARCHParameters:
        """Fit GARCH(p,q) model to return series."""
        try:
            returns = returns.dropna()
            
            if len(returns) < 100:
                raise ValueError("Insufficient data for GARCH estimation")
            
            # Initial parameter estimates
            initial_params = [
                np.var(returns) * 0.1,  # omega
                0.1,                     # alpha
                0.8                      # beta
            ]
            
            # Constraints: omega > 0, alpha >= 0, beta >= 0, alpha + beta < 1
            constraints = [
                {'type': 'ineq', 'fun': lambda x: x[0]},  # omega > 0
                {'type': 'ineq', 'fun': lambda x: x[1]},  # alpha >= 0
                {'type': 'ineq', 'fun': lambda x: x[2]},  # beta >= 0
                {'type': 'ineq', 'fun': lambda x: 0.999 - x[1] - x[2]}  # alpha + beta < 1
            ]
            
            # Bounds
            bounds = [(1e-6, None), (0, 1), (0, 1)]
            
            # Optimize
            result = minimize(
                self._garch_log_likelihood,
                initial_params,
                args=(returns,),
                method='SLSQP',
                bounds=bounds,
                constraints=constraints
            )
            
            if result.success:
                omega, alpha, beta = result.x
                log_likelihood = -result.fun
                
                # Calculate information criteria
                n_params = 3
                n_obs = len(returns)
                aic = 2 * n_params - 2 * log_likelihood
                bic = np.log(n_obs) * n_params - 2 * log_likelihood
                
                return GARCHParameters(
                    omega=omega,
                    alpha=alpha,
                    beta=beta,
                    log_likelihood=log_likelihood,
                    aic=aic,
                    bic=bic
                )
            else:
                raise ValueError("GARCH optimization failed")
                
        except Exception as e:
            self.logger.error(f"GARCH model fitting failed: {e}")
            return GARCHParameters(0.0, 0.0, 0.0, 0.0, np.inf, np.inf)
    
    def _garch_log_likelihood(self, params: List[float], returns: pd.Series) -> float:
        """Calculate negative log-likelihood for GARCH model."""
        try:
            omega, alpha, beta = params
            
            # Initialize conditional variance
            sigma2 = np.var(returns)
            log_likelihood = 0.0
            
            for i, ret in enumerate(returns):
                if i > 0:
                    sigma2 = omega + alpha * returns.iloc[i-1]**2 + beta * sigma2
                
                if sigma2 <= 0:
                    return 1e10  # Return large value for invalid parameters
                
                log_likelihood += -0.5 * (np.log(2 * np.pi) + np.log(sigma2) + ret**2 / sigma2)
            
            return -log_likelihood
            
        except Exception:
            return 1e10
    
    def forecast_volatility_garch(
        self,
        returns: pd.Series,
        horizon: int = 30,
        garch_params: Optional[GARCHParameters] = None
    ) -> VolatilityForecast:
        """Forecast volatility using GARCH model."""
        try:
            if garch_params is None:
                garch_params = self.fit_garch_model(returns)
            
            # Get last return and variance
            last_return = returns.iloc[-1]
            last_variance = np.var(returns.tail(30))
            
            # Forecast volatility
            forecasts = []
            current_variance = last_variance
            
            for h in range(1, horizon + 1):
                if h == 1:
                    forecast_variance = (garch_params.omega + 
                                       garch_params.alpha * last_return**2 + 
                                       garch_params.beta * current_variance)
                else:
                    # Long-term variance
                    long_term_var = garch_params.omega / (1 - garch_params.alpha - garch_params.beta)
                    decay_factor = (garch_params.alpha + garch_params.beta)**(h-1)
                    forecast_variance = long_term_var + decay_factor * (current_variance - long_term_var)
                
                forecasts.append(np.sqrt(forecast_variance * self.trading_days_per_year))
                current_variance = forecast_variance
            
            # Calculate confidence intervals (simplified)
            confidence_intervals = []
            for forecast in forecasts:
                lower = forecast * 0.8
                upper = forecast * 1.2
                confidence_intervals.append((lower, upper))
            
            return VolatilityForecast(
                forecast_values=np.array(forecasts),
                confidence_intervals=confidence_intervals,
                model_type="GARCH",
                forecast_horizon=horizon,
                accuracy_metrics={"aic": garch_params.aic, "bic": garch_params.bic}
            )
            
        except Exception as e:
            self.logger.error(f"GARCH volatility forecasting failed: {e}")
            return VolatilityForecast(
                forecast_values=np.zeros(horizon),
                confidence_intervals=[(0.0, 0.0)] * horizon,
                model_type="GARCH",
                forecast_horizon=horizon,
                accuracy_metrics={}
            )
    
    def calculate_volatility_surface(
        self,
        options_data: pd.DataFrame,
        spot_price: float,
        risk_free_rate: float
    ) -> pd.DataFrame:
        """Calculate implied volatility surface from options data."""
        try:
            surface_data = []
            
            for _, option in options_data.iterrows():
                strike = option['strike']
                expiry = option['expiry_days'] / 365.0
                market_price = option['market_price']
                option_type = option['type']
                
                if expiry > 0 and market_price > 0:
                    # Calculate implied volatility
                    implied_vol = self._calculate_implied_volatility(
                        market_price, spot_price, strike, expiry, 
                        risk_free_rate, option_type
                    )
                    
                    if implied_vol > 0:
                        moneyness = strike / spot_price
                        surface_data.append({
                            'strike': strike,
                            'expiry': expiry,
                            'moneyness': moneyness,
                            'implied_volatility': implied_vol,
                            'option_type': option_type
                        })
            
            return pd.DataFrame(surface_data)
            
        except Exception as e:
            self.logger.error(f"Volatility surface calculation failed: {e}")
            return pd.DataFrame()
    
    def _calculate_implied_volatility(
        self,
        market_price: float,
        spot: float,
        strike: float,
        time_to_expiry: float,
        risk_free_rate: float,
        option_type: str,
        max_iterations: int = 100,
        tolerance: float = 1e-6
    ) -> float:
        """Calculate implied volatility using Newton-Raphson method."""
        try:
            # Initial guess
            volatility = 0.2
            
            for _ in range(max_iterations):
                # Calculate Black-Scholes price and vega
                d1 = (np.log(spot / strike) + (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * np.sqrt(time_to_expiry))
                d2 = d1 - volatility * np.sqrt(time_to_expiry)
                
                if option_type.lower() == 'call':
                    bs_price = spot * norm.cdf(d1) - strike * np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2)
                else:
                    bs_price = strike * np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(-d2) - spot * norm.cdf(-d1)
                
                # Calculate vega
                vega = spot * norm.pdf(d1) * np.sqrt(time_to_expiry)
                
                if abs(vega) < 1e-10:
                    break
                
                # Newton-Raphson update
                price_diff = bs_price - market_price
                volatility_new = volatility - price_diff / vega
                
                if abs(volatility_new - volatility) < tolerance:
                    return max(0.01, volatility_new)
                
                volatility = max(0.01, min(5.0, volatility_new))
            
            return volatility
            
        except Exception as e:
            self.logger.error(f"Implied volatility calculation failed: {e}")
            return 0.0
    
    def calculate_volatility_smile(
        self,
        options_data: pd.DataFrame,
        expiry_target: float,
        tolerance: float = 0.1
    ) -> pd.DataFrame:
        """Extract volatility smile for a specific expiry."""
        try:
            # Filter options near target expiry
            filtered_data = options_data[
                abs(options_data['expiry'] - expiry_target) <= tolerance
            ].copy()
            
            if len(filtered_data) == 0:
                return pd.DataFrame()
            
            # Sort by moneyness
            filtered_data = filtered_data.sort_values('moneyness')
            
            return filtered_data[['moneyness', 'implied_volatility', 'option_type']]
            
        except Exception as e:
            self.logger.error(f"Volatility smile calculation failed: {e}")
            return pd.DataFrame()
    
    def calculate_volatility_term_structure(
        self,
        options_data: pd.DataFrame,
        moneyness_target: float = 1.0,
        tolerance: float = 0.05
    ) -> pd.DataFrame:
        """Extract volatility term structure for ATM options."""
        try:
            # Filter ATM options
            filtered_data = options_data[
                abs(options_data['moneyness'] - moneyness_target) <= tolerance
            ].copy()
            
            if len(filtered_data) == 0:
                return pd.DataFrame()
            
            # Group by expiry and take average IV
            term_structure = filtered_data.groupby('expiry')['implied_volatility'].mean().reset_index()
            term_structure = term_structure.sort_values('expiry')
            
            return term_structure
            
        except Exception as e:
            self.logger.error(f"Volatility term structure calculation failed: {e}")
            return pd.DataFrame()


# Global volatility calculator instance
volatility_calculator = VolatilityCalculator()
