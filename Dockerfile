# QuantEdgeFlow Production Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    gcc \
    g++ \
    git \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Create and set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app/src \
    APP_ENV=production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    libpq5 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r quantuser && useradd -r -g quantuser quantuser

# Create application directory
WORKDIR /app

# Copy Python packages from builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY tests/ ./tests/

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/backups && \
    chown -R quantuser:quantuser /app

# Copy startup script
COPY <<EOF /app/start.sh
#!/bin/bash
set -e

# Wait for database to be ready
echo "Waiting for database..."
while ! pg_isready -h \${DATABASE_HOST:-postgres} -p \${DATABASE_PORT:-5432} -U \${DATABASE_USER:-quantuser}; do
    sleep 2
done
echo "Database is ready!"

# Run database migrations
echo "Running database migrations..."
alembic upgrade head

# Start the application
echo "Starting QuantEdgeFlow..."
exec uvicorn src.main:app --host 0.0.0.0 --port \${PORT:-8000} --workers \${WORKERS:-4}
EOF

# Make startup script executable
RUN chmod +x /app/start.sh

# Copy health check script
COPY <<EOF /app/healthcheck.sh
#!/bin/bash
curl -f http://localhost:\${PORT:-8000}/health || exit 1
EOF

RUN chmod +x /app/healthcheck.sh

# Switch to non-root user
USER quantuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /app/healthcheck.sh

# Default command
CMD ["/app/start.sh"]

# Development stage (for local development)
FROM production as development

# Switch back to root for development tools
USER root

# Install development dependencies
RUN apt-get update && apt-get install -y \
    vim \
    htop \
    postgresql-client \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# Install development Python packages
RUN pip install --no-cache-dir \
    pytest-watch \
    ipython \
    jupyter \
    black \
    flake8 \
    mypy \
    pre-commit

# Copy development configuration
COPY .env.example .env

# Create development startup script
COPY <<EOF /app/start-dev.sh
#!/bin/bash
set -e

echo "Starting QuantEdgeFlow in development mode..."

# Install pre-commit hooks
pre-commit install || true

# Run in development mode with auto-reload
exec uvicorn src.main:app --host 0.0.0.0 --port \${PORT:-8000} --reload --log-level debug
EOF

RUN chmod +x /app/start-dev.sh

# Switch back to quantuser
USER quantuser

# Override command for development
CMD ["/app/start-dev.sh"]

# Testing stage
FROM development as testing

USER root

# Install additional testing tools
RUN pip install --no-cache-dir \
    pytest-cov \
    pytest-mock \
    pytest-asyncio \
    factory-boy \
    faker

# Copy test configuration
COPY pytest.ini .
COPY .coveragerc .

# Create test startup script
COPY <<EOF /app/run-tests.sh
#!/bin/bash
set -e

echo "Running QuantEdgeFlow tests..."

# Wait for test database
echo "Waiting for test database..."
while ! pg_isready -h \${TEST_DATABASE_HOST:-postgres} -p \${TEST_DATABASE_PORT:-5432} -U \${TEST_DATABASE_USER:-quantuser}; do
    sleep 2
done

# Run tests with coverage
pytest tests/ --cov=src --cov-report=html --cov-report=term-missing --cov-fail-under=80 -v

echo "Tests completed!"
EOF

RUN chmod +x /app/run-tests.sh

USER quantuser

# Override command for testing
CMD ["/app/run-tests.sh"]

# Labels for metadata
LABEL maintainer="HectorTa1989 <<EMAIL>>" \
      version="1.0.0" \
      description="QuantEdgeFlow - Elite Options Trading Analysis Platform" \
      org.opencontainers.image.source="https://github.com/HectorTa1989/quantedgeflow" \
      org.opencontainers.image.documentation="https://github.com/HectorTa1989/quantedgeflow/blob/main/README.md" \
      org.opencontainers.image.licenses="MIT"
