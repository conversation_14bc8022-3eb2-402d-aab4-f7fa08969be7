"""
Notification Service for QuantEdgeFlow
Multi-channel notification system for alerts, trades, and portfolio updates
"""

import logging
import asyncio
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum
import json
import aiohttp
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib

from src.config.settings import get_settings
from src.utils.logger import get_audit_logger


settings = get_settings()
logger = logging.getLogger(__name__)
audit_logger = get_audit_logger()


class NotificationType(str, Enum):
    """Notification type enumeration."""
    TRADE_EXECUTED = "trade_executed"
    TRADE_FILLED = "trade_filled"
    TRADE_CANCELLED = "trade_cancelled"
    PORTFOLIO_ALERT = "portfolio_alert"
    RISK_ALERT = "risk_alert"
    MARKET_ALERT = "market_alert"
    SYSTEM_ALERT = "system_alert"
    PERFORMANCE_REPORT = "performance_report"


class NotificationChannel(str, Enum):
    """Notification channel enumeration."""
    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"
    SMS = "sms"
    PUSH = "push"
    IN_APP = "in_app"


class NotificationPriority(str, Enum):
    """Notification priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class NotificationService:
    """Multi-channel notification service."""
    
    def __init__(self):
        self.logger = logger
        self.enabled_channels = self._get_enabled_channels()
        self.rate_limits = {
            NotificationChannel.EMAIL: {"max_per_hour": 50, "sent_count": 0, "reset_time": datetime.utcnow()},
            NotificationChannel.SLACK: {"max_per_hour": 100, "sent_count": 0, "reset_time": datetime.utcnow()},
            NotificationChannel.WEBHOOK: {"max_per_hour": 200, "sent_count": 0, "reset_time": datetime.utcnow()},
        }
    
    def _get_enabled_channels(self) -> List[NotificationChannel]:
        """Get enabled notification channels based on configuration."""
        channels = []
        
        if settings.SLACK_WEBHOOK_URL:
            channels.append(NotificationChannel.SLACK)
        
        if settings.NOTIFICATION_WEBHOOK_URL:
            channels.append(NotificationChannel.WEBHOOK)
        
        # Email would require SMTP configuration
        # channels.append(NotificationChannel.EMAIL)
        
        return channels
    
    async def send_notification(
        self,
        user_id: str,
        notification_type: NotificationType,
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        channels: Optional[List[NotificationChannel]] = None,
        priority: NotificationPriority = NotificationPriority.MEDIUM
    ) -> Dict[str, bool]:
        """Send notification through specified channels."""
        try:
            if channels is None:
                channels = self.enabled_channels
            
            # Create notification payload
            notification = {
                "id": f"notif_{datetime.utcnow().timestamp()}",
                "user_id": user_id,
                "type": notification_type,
                "title": title,
                "message": message,
                "data": data or {},
                "priority": priority,
                "timestamp": datetime.utcnow().isoformat(),
                "channels": [c.value for c in channels]
            }
            
            # Send through each channel
            results = {}
            tasks = []
            
            for channel in channels:
                if channel in self.enabled_channels:
                    if self._check_rate_limit(channel):
                        task = self._send_to_channel(channel, notification)
                        tasks.append((channel, task))
                    else:
                        results[channel.value] = False
                        self.logger.warning(f"Rate limit exceeded for channel {channel}", extra={
                            "user_id": user_id,
                            "notification_type": notification_type
                        })
            
            # Execute all channel sends concurrently
            if tasks:
                channel_results = await asyncio.gather(
                    *[task for _, task in tasks],
                    return_exceptions=True
                )
                
                for i, (channel, _) in enumerate(tasks):
                    result = channel_results[i]
                    if isinstance(result, Exception):
                        results[channel.value] = False
                        self.logger.error(f"Failed to send notification to {channel}: {result}")
                    else:
                        results[channel.value] = result
                        if result:
                            self._increment_rate_limit(channel)
            
            # Log notification attempt
            audit_logger.info(f"Notification sent", extra={
                "user_id": user_id,
                "notification_type": notification_type,
                "channels": [c.value for c in channels],
                "results": results,
                "priority": priority
            })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to send notification: {e}", extra={
                "user_id": user_id,
                "notification_type": notification_type
            })
            return {channel.value: False for channel in (channels or [])}
    
    async def send_trade_notification(
        self,
        user_id: str,
        trade_data: Dict[str, Any],
        notification_type: NotificationType = NotificationType.TRADE_EXECUTED
    ) -> Dict[str, bool]:
        """Send trade-specific notification."""
        try:
            # Format trade message
            symbol = trade_data.get("symbol", "Unknown")
            trade_type = trade_data.get("trade_type", "Unknown")
            quantity = trade_data.get("quantity", 0)
            price = trade_data.get("price", 0)
            
            if notification_type == NotificationType.TRADE_EXECUTED:
                title = f"Trade Executed: {symbol}"
                message = f"{trade_type} {quantity} shares of {symbol} at ${price:.2f}"
            elif notification_type == NotificationType.TRADE_FILLED:
                title = f"Trade Filled: {symbol}"
                message = f"{trade_type} {quantity} shares of {symbol} filled at ${price:.2f}"
            elif notification_type == NotificationType.TRADE_CANCELLED:
                title = f"Trade Cancelled: {symbol}"
                message = f"{trade_type} order for {quantity} shares of {symbol} was cancelled"
            else:
                title = f"Trade Update: {symbol}"
                message = f"Trade update for {symbol}"
            
            return await self.send_notification(
                user_id=user_id,
                notification_type=notification_type,
                title=title,
                message=message,
                data=trade_data,
                priority=NotificationPriority.HIGH
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send trade notification: {e}")
            return {}
    
    async def send_portfolio_alert(
        self,
        user_id: str,
        portfolio_id: str,
        alert_type: str,
        message: str,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, bool]:
        """Send portfolio-specific alert."""
        try:
            title = f"Portfolio Alert: {alert_type}"
            
            # Determine priority based on alert type
            priority = NotificationPriority.MEDIUM
            if alert_type.lower() in ["risk_limit_exceeded", "margin_call", "stop_loss_triggered"]:
                priority = NotificationPriority.CRITICAL
            elif alert_type.lower() in ["profit_target_reached", "position_closed"]:
                priority = NotificationPriority.HIGH
            
            alert_data = {
                "portfolio_id": portfolio_id,
                "alert_type": alert_type,
                **(data or {})
            }
            
            return await self.send_notification(
                user_id=user_id,
                notification_type=NotificationType.PORTFOLIO_ALERT,
                title=title,
                message=message,
                data=alert_data,
                priority=priority
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send portfolio alert: {e}")
            return {}
    
    async def send_risk_alert(
        self,
        user_id: str,
        risk_metric: str,
        current_value: float,
        threshold: float,
        portfolio_id: Optional[str] = None
    ) -> Dict[str, bool]:
        """Send risk management alert."""
        try:
            title = f"Risk Alert: {risk_metric}"
            message = f"{risk_metric} is {current_value:.2f}, exceeding threshold of {threshold:.2f}"
            
            risk_data = {
                "risk_metric": risk_metric,
                "current_value": current_value,
                "threshold": threshold,
                "portfolio_id": portfolio_id
            }
            
            return await self.send_notification(
                user_id=user_id,
                notification_type=NotificationType.RISK_ALERT,
                title=title,
                message=message,
                data=risk_data,
                priority=NotificationPriority.CRITICAL
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send risk alert: {e}")
            return {}
    
    async def send_performance_report(
        self,
        user_id: str,
        report_data: Dict[str, Any],
        report_period: str = "daily"
    ) -> Dict[str, bool]:
        """Send performance report notification."""
        try:
            title = f"Performance Report - {report_period.title()}"
            
            # Format performance summary
            total_return = report_data.get("total_return_pct", 0)
            win_rate = report_data.get("win_rate", 0)
            
            message = f"Portfolio Performance: {total_return:+.2f}% return, {win_rate:.1f}% win rate"
            
            return await self.send_notification(
                user_id=user_id,
                notification_type=NotificationType.PERFORMANCE_REPORT,
                title=title,
                message=message,
                data=report_data,
                priority=NotificationPriority.LOW
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send performance report: {e}")
            return {}
    
    async def _send_to_channel(
        self,
        channel: NotificationChannel,
        notification: Dict[str, Any]
    ) -> bool:
        """Send notification to specific channel."""
        try:
            if channel == NotificationChannel.SLACK:
                return await self._send_slack_notification(notification)
            elif channel == NotificationChannel.WEBHOOK:
                return await self._send_webhook_notification(notification)
            elif channel == NotificationChannel.EMAIL:
                return await self._send_email_notification(notification)
            else:
                self.logger.warning(f"Unsupported notification channel: {channel}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to send to channel {channel}: {e}")
            return False
    
    async def _send_slack_notification(self, notification: Dict[str, Any]) -> bool:
        """Send notification to Slack."""
        if not settings.SLACK_WEBHOOK_URL:
            return False
        
        try:
            # Format Slack message
            color = self._get_slack_color(notification["priority"])
            
            slack_payload = {
                "attachments": [{
                    "color": color,
                    "title": notification["title"],
                    "text": notification["message"],
                    "fields": [
                        {"title": "Type", "value": notification["type"], "short": True},
                        {"title": "Priority", "value": notification["priority"], "short": True},
                        {"title": "User", "value": notification["user_id"], "short": True},
                        {"title": "Time", "value": notification["timestamp"], "short": True}
                    ],
                    "footer": "QuantEdgeFlow",
                    "ts": datetime.utcnow().timestamp()
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    settings.SLACK_WEBHOOK_URL,
                    json=slack_payload,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            self.logger.error(f"Failed to send Slack notification: {e}")
            return False
    
    async def _send_webhook_notification(self, notification: Dict[str, Any]) -> bool:
        """Send notification to webhook."""
        if not settings.NOTIFICATION_WEBHOOK_URL:
            return False
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    settings.NOTIFICATION_WEBHOOK_URL,
                    json=notification,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            self.logger.error(f"Failed to send webhook notification: {e}")
            return False
    
    async def _send_email_notification(self, notification: Dict[str, Any]) -> bool:
        """Send email notification."""
        # Email implementation would require SMTP configuration
        # This is a placeholder
        return False
    
    def _get_slack_color(self, priority: NotificationPriority) -> str:
        """Get Slack color based on priority."""
        color_map = {
            NotificationPriority.LOW: "#36a64f",      # Green
            NotificationPriority.MEDIUM: "#ff9500",   # Orange
            NotificationPriority.HIGH: "#ff0000",     # Red
            NotificationPriority.CRITICAL: "#8B0000"  # Dark Red
        }
        return color_map.get(priority, "#36a64f")
    
    def _check_rate_limit(self, channel: NotificationChannel) -> bool:
        """Check if channel is within rate limits."""
        if channel not in self.rate_limits:
            return True
        
        limit_info = self.rate_limits[channel]
        now = datetime.utcnow()
        
        # Reset counter if an hour has passed
        if (now - limit_info["reset_time"]).total_seconds() >= 3600:
            limit_info["sent_count"] = 0
            limit_info["reset_time"] = now
        
        return limit_info["sent_count"] < limit_info["max_per_hour"]
    
    def _increment_rate_limit(self, channel: NotificationChannel) -> None:
        """Increment rate limit counter for channel."""
        if channel in self.rate_limits:
            self.rate_limits[channel]["sent_count"] += 1


# Global notification service instance
notification_service = NotificationService()
