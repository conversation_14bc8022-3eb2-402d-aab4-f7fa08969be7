"""
Analytics Package for QuantEdgeFlow
Advanced analytics, risk metrics, and signal generation for options trading
"""

# Options pricing
from .options_pricing import (
    OptionsPricingEngine,
    OptionPricingInputs,
    OptionPricingResult,
    options_pricing_engine
)

# Portfolio optimization
from .portfolio_optimizer import (
    PortfolioOptimizer,
    OptimizationConstraints,
    TradeCandidate,
    portfolio_optimizer
)

# Risk metrics
from .risk_metrics import (
    RiskMetricsCalculator,
    risk_calculator
)

# Signal generation
from .signal_generator import (
    SignalGenerator,
    TradingSignal,
    SignalStrength,
    SignalDirection,
    MarketRegime,
    signal_generator
)

# Technical indicators
from .technical_indicators import (
    TechnicalIndicators
)

__all__ = [
    # Options pricing
    "OptionsPricingEngine",
    "OptionPricingInputs", 
    "OptionPricingResult",
    "options_pricing_engine",
    
    # Portfolio optimization
    "PortfolioOptimizer",
    "OptimizationConstraints",
    "TradeCandidate",
    "portfolio_optimizer",
    
    # Risk metrics
    "RiskMetricsCalculator",
    "risk_calculator",
    
    # Signal generation
    "SignalGenerator",
    "TradingSignal",
    "SignalStrength",
    "SignalDirection", 
    "MarketRegime",
    "signal_generator",
    
    # Technical indicators
    "TechnicalIndicators"
]
