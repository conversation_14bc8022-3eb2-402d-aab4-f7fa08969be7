"""
Black-Scholes Options Pricing Model Implementation
Advanced implementation with Greeks calculation and volatility adjustments
"""

import math
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from scipy.stats import norm
import numpy as np


@dataclass
class OptionParameters:
    """Option parameters for pricing calculations."""
    spot_price: float
    strike_price: float
    time_to_expiration: float  # in years
    risk_free_rate: float
    volatility: float
    dividend_yield: float = 0.0
    option_type: str = "call"  # "call" or "put"


@dataclass
class OptionPricing:
    """Option pricing results with Greeks."""
    price: float
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    implied_volatility: Optional[float] = None


class BlackScholesCalculator:
    """Advanced Black-Scholes options pricing calculator."""
    
    def __init__(self):
        self.precision = 1e-6
        self.max_iterations = 100
    
    def calculate_d1_d2(self, params: OptionParameters) -> Tuple[float, float]:
        """Calculate d1 and d2 parameters for Black-Scholes formula."""
        S = params.spot_price
        K = params.strike_price
        T = params.time_to_expiration
        r = params.risk_free_rate
        sigma = params.volatility
        q = params.dividend_yield
        
        if T <= 0:
            raise ValueError("Time to expiration must be positive")
        
        if sigma <= 0:
            raise ValueError("Volatility must be positive")
        
        d1 = (math.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        return d1, d2
    
    def calculate_option_price(self, params: OptionParameters) -> float:
        """Calculate option price using Black-Scholes formula."""
        S = params.spot_price
        K = params.strike_price
        T = params.time_to_expiration
        r = params.risk_free_rate
        q = params.dividend_yield
        
        if T <= 0:
            # Option has expired
            if params.option_type.lower() == "call":
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        d1, d2 = self.calculate_d1_d2(params)
        
        if params.option_type.lower() == "call":
            price = (S * math.exp(-q * T) * norm.cdf(d1) - 
                    K * math.exp(-r * T) * norm.cdf(d2))
        else:  # put
            price = (K * math.exp(-r * T) * norm.cdf(-d2) - 
                    S * math.exp(-q * T) * norm.cdf(-d1))
        
        return max(price, 0)  # Price cannot be negative
    
    def calculate_delta(self, params: OptionParameters) -> float:
        """Calculate option delta (price sensitivity to underlying)."""
        if params.time_to_expiration <= 0:
            if params.option_type.lower() == "call":
                return 1.0 if params.spot_price > params.strike_price else 0.0
            else:
                return -1.0 if params.spot_price < params.strike_price else 0.0
        
        d1, _ = self.calculate_d1_d2(params)
        q = params.dividend_yield
        T = params.time_to_expiration
        
        if params.option_type.lower() == "call":
            delta = math.exp(-q * T) * norm.cdf(d1)
        else:  # put
            delta = -math.exp(-q * T) * norm.cdf(-d1)
        
        return delta
    
    def calculate_gamma(self, params: OptionParameters) -> float:
        """Calculate option gamma (delta sensitivity to underlying)."""
        if params.time_to_expiration <= 0:
            return 0.0
        
        S = params.spot_price
        T = params.time_to_expiration
        sigma = params.volatility
        q = params.dividend_yield
        
        d1, _ = self.calculate_d1_d2(params)
        
        gamma = (math.exp(-q * T) * norm.pdf(d1)) / (S * sigma * math.sqrt(T))
        return gamma
    
    def calculate_theta(self, params: OptionParameters) -> float:
        """Calculate option theta (time decay)."""
        if params.time_to_expiration <= 0:
            return 0.0
        
        S = params.spot_price
        K = params.strike_price
        T = params.time_to_expiration
        r = params.risk_free_rate
        sigma = params.volatility
        q = params.dividend_yield
        
        d1, d2 = self.calculate_d1_d2(params)
        
        term1 = -(S * norm.pdf(d1) * sigma * math.exp(-q * T)) / (2 * math.sqrt(T))
        
        if params.option_type.lower() == "call":
            term2 = r * K * math.exp(-r * T) * norm.cdf(d2)
            term3 = -q * S * math.exp(-q * T) * norm.cdf(d1)
            theta = term1 - term2 + term3
        else:  # put
            term2 = -r * K * math.exp(-r * T) * norm.cdf(-d2)
            term3 = q * S * math.exp(-q * T) * norm.cdf(-d1)
            theta = term1 + term2 + term3
        
        return theta / 365  # Convert to daily theta
    
    def calculate_vega(self, params: OptionParameters) -> float:
        """Calculate option vega (volatility sensitivity)."""
        if params.time_to_expiration <= 0:
            return 0.0
        
        S = params.spot_price
        T = params.time_to_expiration
        q = params.dividend_yield
        
        d1, _ = self.calculate_d1_d2(params)
        
        vega = S * math.exp(-q * T) * norm.pdf(d1) * math.sqrt(T)
        return vega / 100  # Convert to percentage point sensitivity
    
    def calculate_rho(self, params: OptionParameters) -> float:
        """Calculate option rho (interest rate sensitivity)."""
        if params.time_to_expiration <= 0:
            return 0.0
        
        K = params.strike_price
        T = params.time_to_expiration
        r = params.risk_free_rate
        
        _, d2 = self.calculate_d1_d2(params)
        
        if params.option_type.lower() == "call":
            rho = K * T * math.exp(-r * T) * norm.cdf(d2)
        else:  # put
            rho = -K * T * math.exp(-r * T) * norm.cdf(-d2)
        
        return rho / 100  # Convert to percentage point sensitivity
    
    def calculate_all_greeks(self, params: OptionParameters) -> OptionPricing:
        """Calculate option price and all Greeks."""
        price = self.calculate_option_price(params)
        delta = self.calculate_delta(params)
        gamma = self.calculate_gamma(params)
        theta = self.calculate_theta(params)
        vega = self.calculate_vega(params)
        rho = self.calculate_rho(params)
        
        return OptionPricing(
            price=price,
            delta=delta,
            gamma=gamma,
            theta=theta,
            vega=vega,
            rho=rho
        )
    
    def implied_volatility(self, market_price: float, params: OptionParameters) -> float:
        """Calculate implied volatility using Newton-Raphson method."""
        if market_price <= 0:
            return 0.0
        
        # Initial guess
        volatility = 0.3
        
        for _ in range(self.max_iterations):
            params_copy = OptionParameters(
                spot_price=params.spot_price,
                strike_price=params.strike_price,
                time_to_expiration=params.time_to_expiration,
                risk_free_rate=params.risk_free_rate,
                volatility=volatility,
                dividend_yield=params.dividend_yield,
                option_type=params.option_type
            )
            
            price = self.calculate_option_price(params_copy)
            vega = self.calculate_vega(params_copy)
            
            if abs(price - market_price) < self.precision:
                return volatility
            
            if vega == 0:
                break
            
            # Newton-Raphson update
            volatility = volatility - (price - market_price) / vega
            
            # Ensure volatility stays positive
            volatility = max(volatility, 0.001)
        
        return volatility
