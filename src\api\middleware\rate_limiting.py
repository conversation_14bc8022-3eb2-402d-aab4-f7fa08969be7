"""
Advanced Rate Limiting Middleware for QuantEdgeFlow
Multi-tier rate limiting with Redis backend and intelligent throttling
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Tuple, List
from datetime import datetime, timedelta
import json
import hashlib

import redis.asyncio as redis
from fastapi import HTT<PERSON>Exception, Request, Response
from fastapi.responses import JSONResponse

from src.config.settings import get_settings


settings = get_settings()
logger = logging.getLogger(__name__)


class RateLimitExceeded(HTTPException):
    """Rate limit exceeded exception."""
    
    def __init__(self, detail: str, retry_after: int = None):
        super().__init__(status_code=429, detail=detail)
        self.retry_after = retry_after


class RateLimitTier:
    """Rate limit tier configuration."""
    
    def __init__(
        self,
        name: str,
        requests_per_minute: int,
        requests_per_hour: int,
        requests_per_day: int,
        burst_allowance: int = 10
    ):
        self.name = name
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.requests_per_day = requests_per_day
        self.burst_allowance = burst_allowance


# Rate limit tiers for different user types
RATE_LIMIT_TIERS = {
    "free": RateLimitTier(
        name="free",
        requests_per_minute=10,
        requests_per_hour=100,
        requests_per_day=1000,
        burst_allowance=5
    ),
    "premium": RateLimitTier(
        name="premium",
        requests_per_minute=50,
        requests_per_hour=1000,
        requests_per_day=10000,
        burst_allowance=20
    ),
    "enterprise": RateLimitTier(
        name="enterprise",
        requests_per_minute=200,
        requests_per_hour=5000,
        requests_per_day=50000,
        burst_allowance=50
    ),
    "admin": RateLimitTier(
        name="admin",
        requests_per_minute=1000,
        requests_per_hour=10000,
        requests_per_day=100000,
        burst_allowance=100
    )
}

# Endpoint-specific rate limits
ENDPOINT_LIMITS = {
    "/api/v1/trades": {"multiplier": 0.5},  # More restrictive for trading endpoints
    "/api/v1/portfolio": {"multiplier": 1.0},
    "/api/v1/analytics": {"multiplier": 2.0},  # More lenient for analytics
    "/api/v1/options": {"multiplier": 1.5},
}


class RedisRateLimiter:
    """Redis-based rate limiter with sliding window algorithm."""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.connection_pool = None
    
    async def initialize(self):
        """Initialize Redis connection."""
        try:
            self.connection_pool = redis.ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=settings.REDIS_POOL_SIZE,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={}
            )
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis rate limiter initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis rate limiter: {e}")
            # Fallback to in-memory rate limiting
            self.redis_client = None
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
        if self.connection_pool:
            await self.connection_pool.disconnect()
    
    def _get_key(self, identifier: str, window: str) -> str:
        """Generate Redis key for rate limiting."""
        return f"rate_limit:{identifier}:{window}"
    
    async def _sliding_window_check(
        self, 
        identifier: str, 
        window_seconds: int, 
        limit: int
    ) -> Tuple[bool, int, int]:
        """
        Sliding window rate limit check.
        Returns: (allowed, current_count, reset_time)
        """
        if not self.redis_client:
            # Fallback to allowing all requests if Redis is unavailable
            return True, 0, 0
        
        now = time.time()
        window_start = now - window_seconds
        key = self._get_key(identifier, f"window_{window_seconds}")
        
        try:
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            # Remove expired entries
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(now): now})
            
            # Set expiration
            pipe.expire(key, window_seconds + 1)
            
            results = await pipe.execute()
            current_count = results[1] + 1  # +1 for the current request
            
            # Check if limit exceeded
            if current_count > limit:
                # Remove the request we just added since it's rejected
                await self.redis_client.zrem(key, str(now))
                reset_time = int(now + window_seconds)
                return False, current_count - 1, reset_time
            
            reset_time = int(now + window_seconds)
            return True, current_count, reset_time
            
        except Exception as e:
            logger.error(f"Redis rate limit check failed: {e}")
            # Fallback to allowing request
            return True, 0, 0
    
    async def check_rate_limit(
        self, 
        identifier: str, 
        tier: RateLimitTier,
        endpoint_multiplier: float = 1.0
    ) -> Dict[str, any]:
        """
        Check rate limits across multiple time windows.
        Returns rate limit status and headers.
        """
        # Apply endpoint-specific multipliers
        minute_limit = int(tier.requests_per_minute * endpoint_multiplier)
        hour_limit = int(tier.requests_per_hour * endpoint_multiplier)
        day_limit = int(tier.requests_per_day * endpoint_multiplier)
        
        # Check each time window
        minute_allowed, minute_count, minute_reset = await self._sliding_window_check(
            identifier, 60, minute_limit
        )
        
        hour_allowed, hour_count, hour_reset = await self._sliding_window_check(
            identifier, 3600, hour_limit
        )
        
        day_allowed, day_count, day_reset = await self._sliding_window_check(
            identifier, 86400, day_limit
        )
        
        # Request is allowed only if all windows allow it
        allowed = minute_allowed and hour_allowed and day_allowed
        
        # Determine which limit was hit
        limit_type = None
        retry_after = 0
        
        if not minute_allowed:
            limit_type = "minute"
            retry_after = 60
        elif not hour_allowed:
            limit_type = "hour"
            retry_after = 3600
        elif not day_allowed:
            limit_type = "day"
            retry_after = 86400
        
        return {
            "allowed": allowed,
            "limit_type": limit_type,
            "retry_after": retry_after,
            "headers": {
                "X-RateLimit-Limit-Minute": str(minute_limit),
                "X-RateLimit-Remaining-Minute": str(max(0, minute_limit - minute_count)),
                "X-RateLimit-Reset-Minute": str(minute_reset),
                "X-RateLimit-Limit-Hour": str(hour_limit),
                "X-RateLimit-Remaining-Hour": str(max(0, hour_limit - hour_count)),
                "X-RateLimit-Reset-Hour": str(hour_reset),
                "X-RateLimit-Limit-Day": str(day_limit),
                "X-RateLimit-Remaining-Day": str(max(0, day_limit - day_count)),
                "X-RateLimit-Reset-Day": str(day_reset),
                "X-RateLimit-Tier": tier.name,
            }
        }
    
    async def get_user_tier(self, user_id: str) -> str:
        """Get user's rate limit tier from Redis or database."""
        if not self.redis_client:
            return "free"  # Default tier
        
        try:
            tier = await self.redis_client.get(f"user_tier:{user_id}")
            return tier.decode() if tier else "free"
        except Exception as e:
            logger.error(f"Failed to get user tier: {e}")
            return "free"
    
    async def set_user_tier(self, user_id: str, tier: str):
        """Set user's rate limit tier."""
        if not self.redis_client:
            return
        
        try:
            await self.redis_client.set(f"user_tier:{user_id}", tier, ex=86400)  # Cache for 24 hours
        except Exception as e:
            logger.error(f"Failed to set user tier: {e}")


# Global rate limiter instance
rate_limiter = RedisRateLimiter()


class RateLimitMiddleware:
    """Rate limiting middleware for FastAPI."""
    
    def __init__(self, app):
        self.app = app
    
    def _get_client_identifier(self, request: Request) -> str:
        """Get unique identifier for rate limiting."""
        # Try to get user ID from token
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                from api.middleware.auth import auth_manager
                token = auth_header.split(" ")[1]
                token_data = auth_manager.verify_token(token)
                return f"user:{token_data.user_id}"
            except Exception:
                pass
        
        # Fallback to IP address
        client_ip = request.client.host if request.client else "unknown"
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
    
    def _get_endpoint_multiplier(self, path: str) -> float:
        """Get rate limit multiplier for specific endpoint."""
        for endpoint, config in ENDPOINT_LIMITS.items():
            if path.startswith(endpoint):
                return config["multiplier"]
        return 1.0
    
    async def __call__(self, scope, receive, send):
        """Process rate limiting for each request."""
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        # Create request object for processing
        request = Request(scope, receive)

        # Skip rate limiting for public endpoints
        public_paths = ["/", "/docs", "/redoc", "/openapi.json", "/health"]

        if request.url.path in public_paths:
            await self.app(scope, receive, send)
            return
        
        try:
            # Get client identifier and rate limit tier
            identifier = self._get_client_identifier(request)

            # Determine user tier
            if identifier.startswith("user:"):
                user_id = identifier.split(":", 1)[1]
                tier_name = await rate_limiter.get_user_tier(user_id)
            else:
                tier_name = "free"  # Default for IP-based limiting

            tier = RATE_LIMIT_TIERS.get(tier_name, RATE_LIMIT_TIERS["free"])
            endpoint_multiplier = self._get_endpoint_multiplier(request.url.path)

            # Check rate limits
            rate_limit_result = await rate_limiter.check_rate_limit(
                identifier, tier, endpoint_multiplier
            )

            # Check if rate limit exceeded
            if not rate_limit_result["allowed"]:
                logger.warning("Rate limit exceeded", extra={
                    "identifier": identifier,
                    "tier": tier_name,
                    "limit_type": rate_limit_result["limit_type"],
                    "path": request.url.path,
                    "method": request.method
                })

                response = JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate limit exceeded",
                        "message": f"Too many requests. Limit exceeded for {rate_limit_result['limit_type']} window.",
                        "retry_after": rate_limit_result["retry_after"],
                        "tier": tier_name
                    },
                    headers={
                        "Retry-After": str(rate_limit_result["retry_after"]),
                        **rate_limit_result["headers"]
                    }
                )
                await response(scope, receive, send)
                return

            # Continue to the next middleware/app
            await self.app(scope, receive, send)

        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # If rate limiting fails, continue without it
            await self.app(scope, receive, send)


# Initialize rate limiter on startup
async def init_rate_limiter():
    """Initialize rate limiter."""
    await rate_limiter.initialize()


async def close_rate_limiter():
    """Close rate limiter."""
    await rate_limiter.close()
