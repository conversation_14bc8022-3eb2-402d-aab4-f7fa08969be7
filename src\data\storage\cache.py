"""
Cache Manager for QuantEdgeFlow
Redis-based caching for high-performance data access
"""

import logging
import json
import pickle
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
import redis.asyncio as redis
import pandas as pd

from src.config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class CacheManager:
    """Advanced Redis cache manager."""
    
    def __init__(self):
        self.logger = logger
        self.redis_client: Optional[redis.Redis] = None
        self.connection_pool = None
        
        # Cache TTL settings (in seconds)
        self.ttl_settings = {
            'market_data': 300,      # 5 minutes
            'options_data': 600,     # 10 minutes
            'fundamental_data': 3600, # 1 hour
            'analytics': 1800,       # 30 minutes
            'user_session': 86400,   # 24 hours
            'api_response': 60       # 1 minute
        }
    
    async def initialize(self):
        """Initialize Redis connection."""
        try:
            self.connection_pool = redis.ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=settings.REDIS_POOL_SIZE,
                retry_on_timeout=True,
                socket_keepalive=True
            )
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # Test connection
            await self.redis_client.ping()
            self.logger.info("Cache manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize cache manager: {e}")
            self.redis_client = None
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
        if self.connection_pool:
            await self.connection_pool.disconnect()
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        cache_type: str = 'default'
    ) -> bool:
        """Set value in cache."""
        try:
            if not self.redis_client:
                return False
            
            # Determine TTL
            if ttl is None:
                ttl = self.ttl_settings.get(cache_type, 300)
            
            # Serialize value
            if isinstance(value, pd.DataFrame):
                serialized_value = pickle.dumps(value)
            elif isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, default=str)
            else:
                serialized_value = str(value)
            
            # Set in Redis
            await self.redis_client.setex(key, ttl, serialized_value)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set cache key {key}: {e}")
            return False
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache."""
        try:
            if not self.redis_client:
                return default
            
            value = await self.redis_client.get(key)
            if value is None:
                return default
            
            # Try to deserialize as different types
            try:
                # Try pickle first (for DataFrames)
                return pickle.loads(value)
            except:
                try:
                    # Try JSON
                    return json.loads(value.decode())
                except:
                    # Return as string
                    return value.decode()
                    
        except Exception as e:
            self.logger.error(f"Failed to get cache key {key}: {e}")
            return default
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            if not self.redis_client:
                return False
            
            result = await self.redis_client.delete(key)
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Failed to delete cache key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            if not self.redis_client:
                return False
            
            result = await self.redis_client.exists(key)
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Failed to check cache key {key}: {e}")
            return False
    
    async def set_market_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """Cache market data for a symbol."""
        key = f"market_data:{symbol}"
        return await self.set(key, data, cache_type='market_data')
    
    async def get_market_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Get cached market data for a symbol."""
        key = f"market_data:{symbol}"
        return await self.get(key)
    
    async def set_options_data(self, symbol: str, expiry: str, data: pd.DataFrame) -> bool:
        """Cache options data for a symbol and expiry."""
        key = f"options_data:{symbol}:{expiry}"
        return await self.set(key, data, cache_type='options_data')
    
    async def get_options_data(self, symbol: str, expiry: str) -> Optional[pd.DataFrame]:
        """Get cached options data for a symbol and expiry."""
        key = f"options_data:{symbol}:{expiry}"
        return await self.get(key)
    
    async def set_analytics_result(self, analysis_id: str, result: Dict[str, Any]) -> bool:
        """Cache analytics result."""
        key = f"analytics:{analysis_id}"
        return await self.set(key, result, cache_type='analytics')
    
    async def get_analytics_result(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """Get cached analytics result."""
        key = f"analytics:{analysis_id}"
        return await self.get(key)
    
    async def set_user_session(self, user_id: str, session_data: Dict[str, Any]) -> bool:
        """Cache user session data."""
        key = f"session:{user_id}"
        return await self.set(key, session_data, cache_type='user_session')
    
    async def get_user_session(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get cached user session data."""
        key = f"session:{user_id}"
        return await self.get(key)
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate all keys matching a pattern."""
        try:
            if not self.redis_client:
                return 0
            
            keys = await self.redis_client.keys(pattern)
            if keys:
                deleted = await self.redis_client.delete(*keys)
                self.logger.info(f"Invalidated {deleted} cache keys matching pattern: {pattern}")
                return deleted
            return 0
            
        except Exception as e:
            self.logger.error(f"Failed to invalidate pattern {pattern}: {e}")
            return 0
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            if not self.redis_client:
                return {}
            
            info = await self.redis_client.info()
            
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get cache stats: {e}")
            return {}
    
    async def flush_cache(self, pattern: Optional[str] = None) -> bool:
        """Flush cache (all keys or matching pattern)."""
        try:
            if not self.redis_client:
                return False
            
            if pattern:
                deleted = await self.invalidate_pattern(pattern)
                self.logger.info(f"Flushed {deleted} cache keys matching pattern: {pattern}")
            else:
                await self.redis_client.flushdb()
                self.logger.info("Flushed entire cache database")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to flush cache: {e}")
            return False
    
    def _generate_cache_key(self, prefix: str, *args) -> str:
        """Generate standardized cache key."""
        key_parts = [prefix] + [str(arg) for arg in args]
        return ":".join(key_parts)
    
    async def set_with_tags(self, key: str, value: Any, tags: List[str], ttl: Optional[int] = None) -> bool:
        """Set value with tags for group invalidation."""
        try:
            # Set the main value
            success = await self.set(key, value, ttl)
            
            if success and tags:
                # Add key to tag sets
                for tag in tags:
                    tag_key = f"tag:{tag}"
                    await self.redis_client.sadd(tag_key, key)
                    if ttl:
                        await self.redis_client.expire(tag_key, ttl + 3600)  # Tag expires 1 hour after data
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to set cache with tags {key}: {e}")
            return False
    
    async def invalidate_by_tag(self, tag: str) -> int:
        """Invalidate all keys with a specific tag."""
        try:
            if not self.redis_client:
                return 0
            
            tag_key = f"tag:{tag}"
            keys = await self.redis_client.smembers(tag_key)
            
            if keys:
                # Delete all keys with this tag
                deleted = await self.redis_client.delete(*keys)
                # Delete the tag set itself
                await self.redis_client.delete(tag_key)
                
                self.logger.info(f"Invalidated {deleted} cache keys with tag: {tag}")
                return deleted
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Failed to invalidate by tag {tag}: {e}")
            return 0


# Global cache manager instance
cache_manager = CacheManager()
