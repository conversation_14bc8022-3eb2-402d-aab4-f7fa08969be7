#!/usr/bin/env python3
"""
Quick script to fix relative imports in the QuantEdgeFlow project
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path: Path):
    """Fix imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix relative imports to absolute imports
        patterns = [
            (r'^from config\.', 'from src.config.'),
            (r'^from api\.', 'from src.api.'),
            (r'^from core\.', 'from src.core.'),
            (r'^from data\.', 'from src.data.'),
            (r'^from services\.', 'from src.services.'),
            (r'^from utils\.', 'from src.utils.'),
            (r'^import config\.', 'import src.config.'),
            (r'^import api\.', 'import src.api.'),
            (r'^import core\.', 'import src.core.'),
            (r'^import data\.', 'import src.data.'),
            (r'^import services\.', 'import src.services.'),
            (r'^import utils\.', 'import src.utils.'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed imports in {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix all imports."""
    src_dir = Path("src")
    
    if not src_dir.exists():
        print("❌ src directory not found!")
        return
    
    print("🔧 Fixing imports in QuantEdgeFlow project...")
    
    fixed_count = 0
    total_count = 0
    
    # Find all Python files
    for py_file in src_dir.rglob("*.py"):
        if py_file.name == "__init__.py":
            continue  # Skip __init__.py files for now
        
        total_count += 1
        if fix_imports_in_file(py_file):
            fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total files processed: {total_count}")
    print(f"   Files with fixes: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print("\n✅ Import fixes completed!")
    else:
        print("\n✅ No import fixes needed!")

if __name__ == "__main__":
    main()
