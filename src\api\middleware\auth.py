"""
Authentication Middleware for QuantEdgeFlow
JWT-based authentication with role-based access control
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import jwt
from fastapi import HTTPException, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel

from src.config.settings import get_settings


settings = get_settings()
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Security
security = HTTPBearer()


class TokenData(BaseModel):
    """Token payload data structure."""
    user_id: str
    email: str
    roles: List[str] = []
    permissions: List[str] = []
    exp: datetime
    iat: datetime


class UserRole:
    """User role constants."""
    ADMIN = "admin"
    TRADER = "trader"
    ANALYST = "analyst"
    VIEWER = "viewer"


class Permission:
    """Permission constants."""
    READ_PORTFOLIO = "read:portfolio"
    WRITE_PORTFOLIO = "write:portfolio"
    READ_TRADES = "read:trades"
    WRITE_TRADES = "write:trades"
    READ_ANALYTICS = "read:analytics"
    WRITE_ANALYTICS = "write:analytics"
    ADMIN_ACCESS = "admin:access"


# Role-based permissions mapping
ROLE_PERMISSIONS = {
    UserRole.ADMIN: [
        Permission.READ_PORTFOLIO,
        Permission.WRITE_PORTFOLIO,
        Permission.READ_TRADES,
        Permission.WRITE_TRADES,
        Permission.READ_ANALYTICS,
        Permission.WRITE_ANALYTICS,
        Permission.ADMIN_ACCESS,
    ],
    UserRole.TRADER: [
        Permission.READ_PORTFOLIO,
        Permission.WRITE_PORTFOLIO,
        Permission.READ_TRADES,
        Permission.WRITE_TRADES,
        Permission.READ_ANALYTICS,
    ],
    UserRole.ANALYST: [
        Permission.READ_PORTFOLIO,
        Permission.READ_TRADES,
        Permission.READ_ANALYTICS,
        Permission.WRITE_ANALYTICS,
    ],
    UserRole.VIEWER: [
        Permission.READ_PORTFOLIO,
        Permission.READ_TRADES,
        Permission.READ_ANALYTICS,
    ],
}


class AuthenticationError(HTTPException):
    """Custom authentication error."""
    
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(status_code=401, detail=detail)


class AuthorizationError(HTTPException):
    """Custom authorization error."""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(status_code=403, detail=detail)


class AuthManager:
    """Authentication and authorization manager."""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Generate password hash."""
        return pwd_context.hash(password)
    
    def create_access_token(
        self, 
        user_id: str, 
        email: str, 
        roles: List[str] = None,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        # Get permissions based on roles
        permissions = []
        for role in (roles or []):
            permissions.extend(ROLE_PERMISSIONS.get(role, []))
        
        # Remove duplicates
        permissions = list(set(permissions))
        
        to_encode = {
            "user_id": user_id,
            "email": email,
            "roles": roles or [],
            "permissions": permissions,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access_token"
        }
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        
        logger.info("Access token created", extra={
            "user_id": user_id,
            "email": email,
            "roles": roles,
            "expires_at": expire.isoformat()
        })
        
        return encoded_jwt
    
    def verify_token(self, token: str) -> TokenData:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            user_id: str = payload.get("user_id")
            email: str = payload.get("email")
            roles: List[str] = payload.get("roles", [])
            permissions: List[str] = payload.get("permissions", [])
            exp: datetime = datetime.fromtimestamp(payload.get("exp"))
            iat: datetime = datetime.fromtimestamp(payload.get("iat"))
            
            if user_id is None or email is None:
                raise AuthenticationError("Invalid token payload")
            
            # Check if token is expired
            if datetime.utcnow() > exp:
                raise AuthenticationError("Token has expired")
            
            return TokenData(
                user_id=user_id,
                email=email,
                roles=roles,
                permissions=permissions,
                exp=exp,
                iat=iat
            )
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.JWTError as e:
            logger.warning(f"JWT verification failed: {e}")
            raise AuthenticationError("Invalid token")
    
    def check_permission(self, token_data: TokenData, required_permission: str) -> bool:
        """Check if user has required permission."""
        return required_permission in token_data.permissions
    
    def check_role(self, token_data: TokenData, required_role: str) -> bool:
        """Check if user has required role."""
        return required_role in token_data.roles


# Global auth manager instance
auth_manager = AuthManager()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Get current authenticated user ID."""
    try:
        token_data = auth_manager.verify_token(credentials.credentials)
        return token_data.user_id
    except AuthenticationError:
        raise
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise AuthenticationError()


async def get_current_user_with_permissions(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> TokenData:
    """Get current user with full token data including permissions."""
    try:
        return auth_manager.verify_token(credentials.credentials)
    except AuthenticationError:
        raise
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise AuthenticationError()


def require_permission(permission: str):
    """Decorator to require specific permission."""
    def permission_checker(token_data: TokenData = Depends(get_current_user_with_permissions)):
        if not auth_manager.check_permission(token_data, permission):
            logger.warning(f"Permission denied: {permission}", extra={
                "user_id": token_data.user_id,
                "required_permission": permission,
                "user_permissions": token_data.permissions
            })
            raise AuthorizationError(f"Permission required: {permission}")
        return token_data
    return permission_checker


def require_role(role: str):
    """Decorator to require specific role."""
    def role_checker(token_data: TokenData = Depends(get_current_user_with_permissions)):
        if not auth_manager.check_role(token_data, role):
            logger.warning(f"Role access denied: {role}", extra={
                "user_id": token_data.user_id,
                "required_role": role,
                "user_roles": token_data.roles
            })
            raise AuthorizationError(f"Role required: {role}")
        return token_data
    return role_checker


class AuthMiddleware:
    """Authentication middleware for FastAPI."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, request: Request, call_next):
        """Process authentication for each request."""
        # Skip authentication for public endpoints
        public_paths = ["/", "/docs", "/redoc", "/openapi.json", "/health"]
        
        if request.url.path in public_paths:
            return await call_next(request)
        
        # Add request ID for tracking
        import uuid
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Log request
        logger.info("Request received", extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "client_ip": request.client.host if request.client else "unknown"
        })
        
        response = await call_next(request)
        
        # Log response
        logger.info("Request completed", extra={
            "request_id": request_id,
            "status_code": response.status_code
        })
        
        return response
