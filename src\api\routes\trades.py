"""
Trades API Routes for QuantEdgeFlow
RESTful endpoints for trade execution, management, and history
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, date
import uuid

from core.models.trades import (
    TradeCreate, TradeResponse, MultiLegTradeCreate, TradeLegResponse,
    TradeStatus, TradeType, OrderType
)
from services.trades_service import TradesService
from services.notification_service import notification_service, NotificationType
from api.middleware.auth import get_current_user, require_permission, Permission
from utils.helpers import validate_symbol


logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=TradeResponse)
async def create_trade(
    trade_data: TradeCreate,
    portfolio_id: uuid.UUID = Query(..., description="Portfolio ID"),
    user_id: str = Depends(require_permission(Permission.WRITE_TRADES)),
    trades_service: TradesService = Depends()
):
    """Create a new trade order."""
    try:
        validate_symbol(trade_data.symbol)
        
        trade = await trades_service.create_trade(
            user_id=user_id,
            portfolio_id=portfolio_id,
            trade_data=trade_data
        )
        
        if not trade:
            raise HTTPException(
                status_code=400,
                detail="Failed to create trade"
            )
        
        # Send notification
        await notification_service.send_trade_notification(
            user_id=user_id,
            trade_data={
                "trade_id": str(trade.id),
                "symbol": trade.symbol,
                "trade_type": trade.trade_type,
                "quantity": trade.quantity,
                "price": float(trade.price) if trade.price else None,
                "order_type": trade.order_type
            },
            notification_type=NotificationType.TRADE_EXECUTED
        )
        
        logger.info(f"Trade created", extra={
            "user_id": user_id,
            "trade_id": str(trade.id),
            "symbol": trade.symbol,
            "trade_type": trade.trade_type,
            "quantity": trade.quantity
        })
        
        return trade
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create trade: {e}", extra={
            "user_id": user_id,
            "symbol": trade_data.symbol
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/multi-leg", response_model=TradeResponse)
async def create_multi_leg_trade(
    trade_data: MultiLegTradeCreate,
    portfolio_id: uuid.UUID = Query(..., description="Portfolio ID"),
    user_id: str = Depends(require_permission(Permission.WRITE_TRADES)),
    trades_service: TradesService = Depends()
):
    """Create a multi-leg options trade."""
    try:
        # Validate all symbols in legs
        for leg in trade_data.legs:
            validate_symbol(leg.symbol)
        
        trade = await trades_service.create_multi_leg_trade(
            user_id=user_id,
            portfolio_id=portfolio_id,
            trade_data=trade_data
        )
        
        if not trade:
            raise HTTPException(
                status_code=400,
                detail="Failed to create multi-leg trade"
            )
        
        # Send notification
        await notification_service.send_trade_notification(
            user_id=user_id,
            trade_data={
                "trade_id": str(trade.id),
                "strategy_name": trade.strategy_name,
                "trade_strategy": trade.trade_strategy,
                "legs_count": len(trade_data.legs),
                "order_type": trade.order_type
            },
            notification_type=NotificationType.TRADE_EXECUTED
        )
        
        logger.info(f"Multi-leg trade created", extra={
            "user_id": user_id,
            "trade_id": str(trade.id),
            "strategy_name": trade.strategy_name,
            "legs_count": len(trade_data.legs)
        })
        
        return trade
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create multi-leg trade: {e}", extra={
            "user_id": user_id,
            "strategy_name": trade_data.strategy_name
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=List[TradeResponse])
async def get_trades(
    portfolio_id: Optional[uuid.UUID] = Query(None, description="Filter by portfolio ID"),
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    status: Optional[str] = Query(None, description="Filter by trade status"),
    trade_type: Optional[str] = Query(None, description="Filter by trade type"),
    start_date: Optional[date] = Query(None, description="Start date filter"),
    end_date: Optional[date] = Query(None, description="End date filter"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of trades to return"),
    offset: int = Query(0, ge=0, description="Number of trades to skip"),
    user_id: str = Depends(require_permission(Permission.READ_TRADES)),
    trades_service: TradesService = Depends()
):
    """Get trades with filtering options."""
    try:
        if symbol:
            validate_symbol(symbol)
        
        trades = await trades_service.get_trades(
            user_id=user_id,
            portfolio_id=portfolio_id,
            symbol=symbol,
            status=status,
            trade_type=trade_type,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )
        
        logger.info(f"Trades retrieved", extra={
            "user_id": user_id,
            "trades_count": len(trades),
            "portfolio_id": str(portfolio_id) if portfolio_id else None,
            "symbol": symbol
        })
        
        return trades
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get trades: {e}", extra={
            "user_id": user_id
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{trade_id}", response_model=TradeResponse)
async def get_trade(
    trade_id: uuid.UUID = Path(..., description="Trade ID"),
    user_id: str = Depends(require_permission(Permission.READ_TRADES)),
    trades_service: TradesService = Depends()
):
    """Get trade by ID."""
    try:
        trade = await trades_service.get_trade_by_id(trade_id, user_id)
        
        if not trade:
            raise HTTPException(
                status_code=404,
                detail="Trade not found"
            )
        
        return trade
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get trade: {e}", extra={
            "user_id": user_id,
            "trade_id": str(trade_id)
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{trade_id}/cancel", response_model=TradeResponse)
async def cancel_trade(
    trade_id: uuid.UUID = Path(..., description="Trade ID"),
    user_id: str = Depends(require_permission(Permission.WRITE_TRADES)),
    trades_service: TradesService = Depends()
):
    """Cancel a pending trade."""
    try:
        trade = await trades_service.cancel_trade(trade_id, user_id)
        
        if not trade:
            raise HTTPException(
                status_code=404,
                detail="Trade not found or cannot be cancelled"
            )
        
        # Send notification
        await notification_service.send_trade_notification(
            user_id=user_id,
            trade_data={
                "trade_id": str(trade.id),
                "symbol": trade.symbol,
                "trade_type": trade.trade_type,
                "quantity": trade.quantity
            },
            notification_type=NotificationType.TRADE_CANCELLED
        )
        
        logger.info(f"Trade cancelled", extra={
            "user_id": user_id,
            "trade_id": str(trade_id)
        })
        
        return trade
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to cancel trade: {e}", extra={
            "user_id": user_id,
            "trade_id": str(trade_id)
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{trade_id}/legs", response_model=List[TradeLegResponse])
async def get_trade_legs(
    trade_id: uuid.UUID = Path(..., description="Trade ID"),
    user_id: str = Depends(require_permission(Permission.READ_TRADES)),
    trades_service: TradesService = Depends()
):
    """Get legs for a multi-leg trade."""
    try:
        legs = await trades_service.get_trade_legs(trade_id, user_id)
        
        logger.info(f"Trade legs retrieved", extra={
            "user_id": user_id,
            "trade_id": str(trade_id),
            "legs_count": len(legs)
        })
        
        return legs
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get trade legs: {e}", extra={
            "user_id": user_id,
            "trade_id": str(trade_id)
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/history/summary", response_model=Dict[str, Any])
async def get_trading_summary(
    portfolio_id: Optional[uuid.UUID] = Query(None, description="Filter by portfolio ID"),
    start_date: Optional[date] = Query(None, description="Start date for summary"),
    end_date: Optional[date] = Query(None, description="End date for summary"),
    user_id: str = Depends(require_permission(Permission.READ_TRADES)),
    trades_service: TradesService = Depends()
):
    """Get trading summary and statistics."""
    try:
        summary = await trades_service.get_trading_summary(
            user_id=user_id,
            portfolio_id=portfolio_id,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"Trading summary retrieved", extra={
            "user_id": user_id,
            "portfolio_id": str(portfolio_id) if portfolio_id else None,
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None
        })
        
        return summary
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get trading summary: {e}", extra={
            "user_id": user_id
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/performance/metrics", response_model=Dict[str, Any])
async def get_trading_performance(
    portfolio_id: Optional[uuid.UUID] = Query(None, description="Filter by portfolio ID"),
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    strategy: Optional[str] = Query(None, description="Filter by trading strategy"),
    start_date: Optional[date] = Query(None, description="Start date for analysis"),
    end_date: Optional[date] = Query(None, description="End date for analysis"),
    user_id: str = Depends(require_permission(Permission.READ_TRADES)),
    trades_service: TradesService = Depends()
):
    """Get detailed trading performance metrics."""
    try:
        if symbol:
            validate_symbol(symbol)
        
        performance = await trades_service.get_trading_performance(
            user_id=user_id,
            portfolio_id=portfolio_id,
            symbol=symbol,
            strategy=strategy,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"Trading performance retrieved", extra={
            "user_id": user_id,
            "portfolio_id": str(portfolio_id) if portfolio_id else None,
            "symbol": symbol,
            "strategy": strategy
        })
        
        return performance
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get trading performance: {e}", extra={
            "user_id": user_id
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/simulate", response_model=Dict[str, Any])
async def simulate_trade(
    trade_data: TradeCreate,
    portfolio_id: uuid.UUID = Query(..., description="Portfolio ID"),
    user_id: str = Depends(require_permission(Permission.READ_TRADES)),
    trades_service: TradesService = Depends()
):
    """Simulate a trade without executing it."""
    try:
        validate_symbol(trade_data.symbol)
        
        simulation = await trades_service.simulate_trade(
            user_id=user_id,
            portfolio_id=portfolio_id,
            trade_data=trade_data
        )
        
        logger.info(f"Trade simulated", extra={
            "user_id": user_id,
            "symbol": trade_data.symbol,
            "trade_type": trade_data.trade_type,
            "quantity": trade_data.quantity
        })
        
        return simulation
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to simulate trade: {e}", extra={
            "user_id": user_id,
            "symbol": trade_data.symbol
        })
        raise HTTPException(status_code=500, detail="Internal server error")
