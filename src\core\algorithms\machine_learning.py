"""
Machine Learning Models for QuantEdgeFlow
Advanced ML models for options pricing, volatility prediction, and trading signal generation
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings

# Optional imports for advanced models
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


@dataclass
class MLModelResult:
    """Machine learning model result."""
    predictions: np.ndarray
    confidence_scores: np.ndarray
    feature_importance: Dict[str, float]
    model_metrics: Dict[str, float]
    model_type: str


@dataclass
class FeatureSet:
    """Feature set for ML models."""
    technical_features: pd.DataFrame
    fundamental_features: pd.DataFrame
    options_features: pd.DataFrame
    market_features: pd.DataFrame
    target_variable: pd.Series


class MLPredictor:
    """Advanced machine learning predictor for options trading."""
    
    def __init__(self):
        self.logger = logger
        self.models = {}
        self.scalers = {}
        self.feature_names = []
        
        # Model configurations
        self.model_configs = {
            'random_forest': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42
            },
            'gradient_boosting': {
                'n_estimators': 100,
                'learning_rate': 0.1,
                'max_depth': 6,
                'random_state': 42
            },
            'xgboost': {
                'n_estimators': 100,
                'learning_rate': 0.1,
                'max_depth': 6,
                'random_state': 42
            } if XGBOOST_AVAILABLE else None,
            'lightgbm': {
                'n_estimators': 100,
                'learning_rate': 0.1,
                'max_depth': 6,
                'random_state': 42
            } if LIGHTGBM_AVAILABLE else None
        }
    
    def create_features(
        self,
        market_data: pd.DataFrame,
        options_data: Optional[pd.DataFrame] = None,
        fundamental_data: Optional[pd.DataFrame] = None
    ) -> pd.DataFrame:
        """Create comprehensive feature set for ML models."""
        try:
            features = pd.DataFrame(index=market_data.index)
            
            # Technical features
            features = pd.concat([features, self._create_technical_features(market_data)], axis=1)
            
            # Options features
            if options_data is not None:
                features = pd.concat([features, self._create_options_features(options_data)], axis=1)
            
            # Fundamental features
            if fundamental_data is not None:
                features = pd.concat([features, self._create_fundamental_features(fundamental_data)], axis=1)
            
            # Market regime features
            features = pd.concat([features, self._create_market_regime_features(market_data)], axis=1)
            
            # Remove NaN values
            features = features.fillna(method='ffill').fillna(0)
            
            return features
            
        except Exception as e:
            self.logger.error(f"Feature creation failed: {e}")
            return pd.DataFrame()
    
    def _create_technical_features(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Create technical analysis features."""
        try:
            features = pd.DataFrame(index=market_data.index)
            
            # Price-based features
            features['returns_1d'] = market_data['close'].pct_change()
            features['returns_5d'] = market_data['close'].pct_change(5)
            features['returns_20d'] = market_data['close'].pct_change(20)
            
            # Moving averages
            features['sma_5'] = market_data['close'].rolling(5).mean()
            features['sma_20'] = market_data['close'].rolling(20).mean()
            features['sma_50'] = market_data['close'].rolling(50).mean()
            
            # Price ratios
            features['price_sma5_ratio'] = market_data['close'] / features['sma_5']
            features['price_sma20_ratio'] = market_data['close'] / features['sma_20']
            features['sma5_sma20_ratio'] = features['sma_5'] / features['sma_20']
            
            # Volatility features
            features['volatility_5d'] = features['returns_1d'].rolling(5).std()
            features['volatility_20d'] = features['returns_1d'].rolling(20).std()
            features['volatility_ratio'] = features['volatility_5d'] / features['volatility_20d']
            
            # Volume features
            if 'volume' in market_data.columns:
                features['volume_sma_20'] = market_data['volume'].rolling(20).mean()
                features['volume_ratio'] = market_data['volume'] / features['volume_sma_20']
            
            # RSI
            features['rsi'] = self._calculate_rsi(market_data['close'], 14)
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(market_data['close'], 20, 2)
            features['bb_position'] = (market_data['close'] - bb_lower) / (bb_upper - bb_lower)
            features['bb_width'] = (bb_upper - bb_lower) / bb_middle
            
            return features
            
        except Exception as e:
            self.logger.error(f"Technical features creation failed: {e}")
            return pd.DataFrame()
    
    def _create_options_features(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """Create options-specific features."""
        try:
            features = pd.DataFrame(index=options_data.index)
            
            # Implied volatility features
            if 'implied_volatility' in options_data.columns:
                features['iv_mean'] = options_data['implied_volatility']
                features['iv_rank'] = options_data['implied_volatility'].rolling(252).rank(pct=True)
                features['iv_percentile'] = options_data['implied_volatility'].rolling(252).apply(
                    lambda x: (x.iloc[-1] - x.min()) / (x.max() - x.min()) if x.max() != x.min() else 0.5
                )
            
            # Greeks features
            greek_columns = ['delta', 'gamma', 'theta', 'vega', 'rho']
            for greek in greek_columns:
                if greek in options_data.columns:
                    features[f'{greek}_mean'] = options_data[greek]
                    features[f'{greek}_change'] = options_data[greek].pct_change()
            
            # Put/Call ratio
            if 'put_volume' in options_data.columns and 'call_volume' in options_data.columns:
                features['put_call_ratio'] = options_data['put_volume'] / (options_data['call_volume'] + 1e-10)
            
            return features
            
        except Exception as e:
            self.logger.error(f"Options features creation failed: {e}")
            return pd.DataFrame()
    
    def _create_fundamental_features(self, fundamental_data: pd.DataFrame) -> pd.DataFrame:
        """Create fundamental analysis features."""
        try:
            features = pd.DataFrame(index=fundamental_data.index)
            
            # Valuation ratios
            if 'pe_ratio' in fundamental_data.columns:
                features['pe_ratio'] = fundamental_data['pe_ratio']
                features['pe_ratio_change'] = fundamental_data['pe_ratio'].pct_change()
            
            if 'pb_ratio' in fundamental_data.columns:
                features['pb_ratio'] = fundamental_data['pb_ratio']
            
            # Growth metrics
            if 'earnings_growth' in fundamental_data.columns:
                features['earnings_growth'] = fundamental_data['earnings_growth']
            
            if 'revenue_growth' in fundamental_data.columns:
                features['revenue_growth'] = fundamental_data['revenue_growth']
            
            return features
            
        except Exception as e:
            self.logger.error(f"Fundamental features creation failed: {e}")
            return pd.DataFrame()
    
    def _create_market_regime_features(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Create market regime features."""
        try:
            features = pd.DataFrame(index=market_data.index)
            
            # Trend strength
            sma_20 = market_data['close'].rolling(20).mean()
            sma_50 = market_data['close'].rolling(50).mean()
            features['trend_strength'] = (sma_20 - sma_50) / sma_50
            
            # Market volatility regime
            returns = market_data['close'].pct_change()
            features['vol_regime'] = returns.rolling(20).std().rolling(60).rank(pct=True)
            
            # Price momentum
            features['momentum_5d'] = market_data['close'] / market_data['close'].shift(5) - 1
            features['momentum_20d'] = market_data['close'] / market_data['close'].shift(20) - 1
            
            return features
            
        except Exception as e:
            self.logger.error(f"Market regime features creation failed: {e}")
            return pd.DataFrame()
    
    def train_model(
        self,
        features: pd.DataFrame,
        target: pd.Series,
        model_type: str = 'random_forest',
        test_size: float = 0.2,
        validation_split: float = 0.2
    ) -> MLModelResult:
        """Train machine learning model."""
        try:
            # Prepare data
            X = features.dropna()
            y = target.loc[X.index]
            
            if len(X) < 100:
                raise ValueError("Insufficient data for training")
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=42, shuffle=False
            )
            
            # Scale features
            scaler = RobustScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train model
            model = self._get_model(model_type)
            model.fit(X_train_scaled, y_train)
            
            # Make predictions
            train_predictions = model.predict(X_train_scaled)
            test_predictions = model.predict(X_test_scaled)
            
            # Calculate metrics
            train_metrics = self._calculate_metrics(y_train, train_predictions)
            test_metrics = self._calculate_metrics(y_test, test_predictions)
            
            # Feature importance
            feature_importance = self._get_feature_importance(model, X.columns)
            
            # Calculate confidence scores (simplified)
            confidence_scores = np.abs(test_predictions - y_test.mean()) / y_test.std()
            confidence_scores = 1 - np.clip(confidence_scores, 0, 1)
            
            # Store model and scaler
            self.models[model_type] = model
            self.scalers[model_type] = scaler
            self.feature_names = list(X.columns)
            
            return MLModelResult(
                predictions=test_predictions,
                confidence_scores=confidence_scores,
                feature_importance=feature_importance,
                model_metrics={
                    'train_r2': train_metrics['r2'],
                    'test_r2': test_metrics['r2'],
                    'train_rmse': train_metrics['rmse'],
                    'test_rmse': test_metrics['rmse'],
                    'train_mae': train_metrics['mae'],
                    'test_mae': test_metrics['mae']
                },
                model_type=model_type
            )
            
        except Exception as e:
            self.logger.error(f"Model training failed: {e}")
            return MLModelResult(
                predictions=np.array([]),
                confidence_scores=np.array([]),
                feature_importance={},
                model_metrics={},
                model_type=model_type
            )
    
    def predict(
        self,
        features: pd.DataFrame,
        model_type: str = 'random_forest'
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Make predictions using trained model."""
        try:
            if model_type not in self.models:
                raise ValueError(f"Model {model_type} not trained")
            
            model = self.models[model_type]
            scaler = self.scalers[model_type]
            
            # Ensure features match training features
            features_aligned = features[self.feature_names].fillna(0)
            
            # Scale features
            features_scaled = scaler.transform(features_aligned)
            
            # Make predictions
            predictions = model.predict(features_scaled)
            
            # Calculate confidence scores (simplified)
            confidence_scores = np.ones(len(predictions)) * 0.7  # Default confidence
            
            return predictions, confidence_scores
            
        except Exception as e:
            self.logger.error(f"Prediction failed: {e}")
            return np.array([]), np.array([])
    
    def _get_model(self, model_type: str):
        """Get model instance based on type."""
        config = self.model_configs.get(model_type, {})
        
        if model_type == 'random_forest':
            return RandomForestRegressor(**config)
        elif model_type == 'gradient_boosting':
            return GradientBoostingRegressor(**config)
        elif model_type == 'xgboost' and XGBOOST_AVAILABLE:
            return xgb.XGBRegressor(**config)
        elif model_type == 'lightgbm' and LIGHTGBM_AVAILABLE:
            return lgb.LGBMRegressor(**config)
        else:
            return RandomForestRegressor(**self.model_configs['random_forest'])
    
    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate model performance metrics."""
        return {
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mae': mean_absolute_error(y_true, y_pred),
            'r2': r2_score(y_true, y_pred)
        }
    
    def _get_feature_importance(self, model, feature_names: List[str]) -> Dict[str, float]:
        """Get feature importance from model."""
        try:
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
                return dict(zip(feature_names, importance))
            else:
                return {}
        except:
            return {}
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_bollinger_bands(self, prices: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands."""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = sma + (std * num_std)
        lower = sma - (std * num_std)
        return upper, sma, lower


# Global ML predictor instance
ml_predictor = MLPredictor()
