"""
Portfolio Optimization Engine for Options Trading
Advanced portfolio optimization with risk constraints and sector diversification
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from scipy.optimize import minimize
import logging

from src.core.models.trades import Trade, TradeStrategy
from src.core.models.portfolio import Portfolio, PortfolioMetrics
from src.core.algorithms.black_scholes import BlackScholesCalculator, OptionParameters
from src.config.settings import get_settings


logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class OptimizationConstraints:
    """Portfolio optimization constraints."""
    max_portfolio_delta: float = 0.30
    min_portfolio_vega: float = -0.05
    max_trades_per_sector: int = 2
    max_trades_total: int = 5
    min_probability_of_profit: float = 0.65
    min_credit_loss_ratio: float = 0.33
    max_loss_per_trade: float = 500.0  # $500 max loss per trade
    nav: float = 100000.0  # Net Asset Value


@dataclass
class TradeCandidate:
    """Candidate trade for portfolio optimization."""
    ticker: str
    strategy: TradeStrategy
    legs: List[Dict]
    sector: str
    model_score: float
    probability_of_profit: float
    credit_loss_ratio: float
    max_loss: float
    expected_return: float
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    momentum_z: float
    flow_z: float


class PortfolioOptimizer:
    """Advanced portfolio optimizer for options trading."""
    
    def __init__(self):
        self.bs_calculator = BlackScholesCalculator()
        self.constraints = OptimizationConstraints()
        
    def filter_trade_candidates(self, candidates: List[TradeCandidate]) -> List[TradeCandidate]:
        """Apply hard filters to trade candidates."""
        filtered_candidates = []
        
        for candidate in candidates:
            # Hard filter checks
            if (candidate.probability_of_profit >= self.constraints.min_probability_of_profit and
                candidate.credit_loss_ratio >= self.constraints.min_credit_loss_ratio and
                candidate.max_loss <= self.constraints.max_loss_per_trade):
                
                filtered_candidates.append(candidate)
                logger.debug(f"Trade candidate {candidate.ticker} passed hard filters")
            else:
                logger.debug(f"Trade candidate {candidate.ticker} failed hard filters")
        
        return filtered_candidates
    
    def calculate_portfolio_greeks(self, trades: List[TradeCandidate]) -> Dict[str, float]:
        """Calculate portfolio-level Greeks from selected trades."""
        portfolio_delta = sum(trade.delta for trade in trades)
        portfolio_gamma = sum(trade.gamma for trade in trades)
        portfolio_theta = sum(trade.theta for trade in trades)
        portfolio_vega = sum(trade.vega for trade in trades)
        portfolio_rho = sum(trade.rho for trade in trades)
        
        # Normalize by NAV
        nav_factor = self.constraints.nav / 100000
        
        return {
            'delta': portfolio_delta / nav_factor,
            'gamma': portfolio_gamma / nav_factor,
            'theta': portfolio_theta / nav_factor,
            'vega': portfolio_vega / nav_factor,
            'rho': portfolio_rho / nav_factor
        }
    
    def check_portfolio_constraints(self, trades: List[TradeCandidate]) -> bool:
        """Check if portfolio meets risk constraints."""
        if len(trades) > self.constraints.max_trades_total:
            return False
        
        # Check sector diversification
        sector_counts = {}
        for trade in trades:
            sector_counts[trade.sector] = sector_counts.get(trade.sector, 0) + 1
            if sector_counts[trade.sector] > self.constraints.max_trades_per_sector:
                return False
        
        # Check portfolio Greeks
        portfolio_greeks = self.calculate_portfolio_greeks(trades)
        
        if abs(portfolio_greeks['delta']) > self.constraints.max_portfolio_delta:
            return False
        
        if portfolio_greeks['vega'] < self.constraints.min_portfolio_vega:
            return False
        
        return True
    
    def calculate_portfolio_score(self, trades: List[TradeCandidate]) -> float:
        """Calculate overall portfolio score for optimization."""
        if not trades:
            return 0.0
        
        # Weighted score based on model scores
        total_score = sum(trade.model_score for trade in trades)
        
        # Add momentum and flow bonuses
        momentum_bonus = sum(trade.momentum_z for trade in trades) * 0.1
        flow_bonus = sum(trade.flow_z for trade in trades) * 0.1
        
        # Risk-adjusted return consideration
        total_expected_return = sum(trade.expected_return for trade in trades)
        total_max_loss = sum(trade.max_loss for trade in trades)
        
        risk_adjusted_score = total_expected_return / max(total_max_loss, 1.0)
        
        return total_score + momentum_bonus + flow_bonus + risk_adjusted_score
    
    def optimize_portfolio_greedy(self, candidates: List[TradeCandidate]) -> List[TradeCandidate]:
        """Greedy optimization algorithm for trade selection."""
        # Filter candidates first
        filtered_candidates = self.filter_trade_candidates(candidates)
        
        if not filtered_candidates:
            logger.warning("No candidates passed hard filters")
            return []
        
        # Sort by model score (descending)
        sorted_candidates = sorted(
            filtered_candidates,
            key=lambda x: (x.model_score, x.momentum_z, x.flow_z),
            reverse=True
        )
        
        selected_trades = []
        
        for candidate in sorted_candidates:
            # Try adding this trade to the portfolio
            test_portfolio = selected_trades + [candidate]
            
            if self.check_portfolio_constraints(test_portfolio):
                selected_trades.append(candidate)
                logger.info(f"Added trade {candidate.ticker} to portfolio")
                
                if len(selected_trades) >= self.constraints.max_trades_total:
                    break
            else:
                logger.debug(f"Trade {candidate.ticker} violates portfolio constraints")
        
        return selected_trades
    
    def optimize_portfolio_genetic(self, candidates: List[TradeCandidate]) -> List[TradeCandidate]:
        """Genetic algorithm for portfolio optimization (advanced method)."""
        filtered_candidates = self.filter_trade_candidates(candidates)
        
        if len(filtered_candidates) <= self.constraints.max_trades_total:
            return filtered_candidates
        
        # Genetic algorithm parameters
        population_size = 50
        generations = 100
        mutation_rate = 0.1
        
        def create_individual():
            """Create a random portfolio individual."""
            num_trades = np.random.randint(1, self.constraints.max_trades_total + 1)
            selected_indices = np.random.choice(
                len(filtered_candidates),
                size=num_trades,
                replace=False
            )
            return [filtered_candidates[i] for i in selected_indices]
        
        def fitness(individual):
            """Calculate fitness score for an individual."""
            if not self.check_portfolio_constraints(individual):
                return -1000  # Heavy penalty for constraint violations
            return self.calculate_portfolio_score(individual)
        
        # Initialize population
        population = [create_individual() for _ in range(population_size)]
        
        for generation in range(generations):
            # Evaluate fitness
            fitness_scores = [fitness(individual) for individual in population]
            
            # Selection (tournament selection)
            new_population = []
            for _ in range(population_size):
                tournament_indices = np.random.choice(population_size, size=3)
                tournament_fitness = [fitness_scores[i] for i in tournament_indices]
                winner_idx = tournament_indices[np.argmax(tournament_fitness)]
                new_population.append(population[winner_idx].copy())
            
            # Mutation
            for individual in new_population:
                if np.random.random() < mutation_rate:
                    if len(individual) > 1:
                        # Remove a random trade
                        individual.pop(np.random.randint(len(individual)))
                    
                    # Add a random trade if possible
                    if len(individual) < self.constraints.max_trades_total:
                        available_candidates = [
                            c for c in filtered_candidates if c not in individual
                        ]
                        if available_candidates:
                            individual.append(np.random.choice(available_candidates))
            
            population = new_population
        
        # Return best individual
        final_fitness = [fitness(individual) for individual in population]
        best_individual = population[np.argmax(final_fitness)]
        
        return best_individual
    
    def optimize_portfolio(self, candidates: List[TradeCandidate], 
                          method: str = "greedy") -> List[TradeCandidate]:
        """Main portfolio optimization method."""
        logger.info(f"Starting portfolio optimization with {len(candidates)} candidates")
        
        if method == "greedy":
            selected_trades = self.optimize_portfolio_greedy(candidates)
        elif method == "genetic":
            selected_trades = self.optimize_portfolio_genetic(candidates)
        else:
            raise ValueError(f"Unknown optimization method: {method}")
        
        # Final validation
        if not self.check_portfolio_constraints(selected_trades):
            logger.error("Final portfolio violates constraints")
            return []
        
        portfolio_greeks = self.calculate_portfolio_greeks(selected_trades)
        portfolio_score = self.calculate_portfolio_score(selected_trades)
        
        logger.info(f"Portfolio optimization complete:")
        logger.info(f"  Selected trades: {len(selected_trades)}")
        logger.info(f"  Portfolio Delta: {portfolio_greeks['delta']:.4f}")
        logger.info(f"  Portfolio Vega: {portfolio_greeks['vega']:.4f}")
        logger.info(f"  Portfolio Score: {portfolio_score:.4f}")
        
        return selected_trades
    
    def generate_trade_report(self, selected_trades: List[TradeCandidate]) -> pd.DataFrame:
        """Generate a formatted trade report."""
        if not selected_trades:
            return pd.DataFrame()
        
        report_data = []
        for trade in selected_trades:
            legs_str = f"{len(trade.legs)} legs"  # Simplified for display
            thesis = f"Model score {trade.model_score:.2f}, POP {trade.probability_of_profit:.1%}"
            
            report_data.append({
                'Ticker': trade.ticker,
                'Strategy': trade.strategy.value if hasattr(trade.strategy, 'value') else str(trade.strategy),
                'Legs': legs_str,
                'Thesis': thesis[:30] + "..." if len(thesis) > 30 else thesis,
                'POP': f"{trade.probability_of_profit:.1%}"
            })
        
        return pd.DataFrame(report_data)
