# QuantEdgeFlow .gitignore

# Environment and Configuration
.env
.env.local
.env.production
.env.staging
.env.development
config/local_settings.py
secrets/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.conda/

# IDEs and Editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Logs
logs/
*.log
*.log.*
log/

# Database
*.db
*.sqlite
*.sqlite3
db.sqlite3
database.db

# Redis dump
dump.rdb

# Backup files
*.bak
*.backup
*.sql.gz
backups/

# Data files
data/
*.csv
*.json
*.parquet
*.h5
*.hdf5
*.pickle
*.pkl

# Machine Learning Models
models/
*.model
*.joblib
*.h5
*.pb
*.onnx
checkpoints/

# Temporary files
tmp/
temp/
.tmp/
*.tmp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# Kubernetes
k8s/secrets/
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# AWS
.aws/

# SSL Certificates
*.pem
*.key
*.crt
*.csr
ssl/
certs/

# API Keys and Secrets
api_keys.txt
secrets.txt
*.secret

# Market Data
market_data/
historical_data/
options_data/
fundamental_data/

# Reports and Analysis
reports/
analysis/
*.pdf
*.xlsx
*.docx

# Frontend (if applicable)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/
dist/
build/

# Monitoring and Metrics
prometheus_data/
grafana_data/
metrics/

# Cache
.cache/
cache/
*.cache

# Profiling
*.prof
profile_results/

# Documentation builds
docs/_build/
docs/build/

# Local development
local/
dev/
sandbox/

# Backup and Archive
*.zip
*.tar.gz
*.rar
*.7z

# Financial Data (sensitive)
portfolio_data/
trade_history/
pnl_reports/
risk_reports/

# Configuration overrides
local_config.py
override_settings.py

# IDE specific
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# MacOS
.AppleDouble
.LSOverride

# Windows
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Vim
*.swp
*.swo
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Visual Studio Code
.vscode/
*.code-workspace

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix
