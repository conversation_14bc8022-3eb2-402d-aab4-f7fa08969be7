"""
Portfolio Management API Routes
RESTful endpoints for portfolio operations and analysis
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime

from core.models.portfolio import Portfolio, PortfolioMetrics, PortfolioPosition
from core.models.trades import Trade, TradeStatus
from services.portfolio_service import PortfolioService
from services.analytics_service import AnalyticsService
from api.middleware.auth import get_current_user
from utils.helpers import validate_symbol


logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
async def get_portfolio_overview(
    user_id: str = Depends(get_current_user),
    portfolio_service: PortfolioService = Depends()
):
    """Get comprehensive portfolio overview."""
    try:
        portfolio = await portfolio_service.get_user_portfolio(user_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        metrics = await portfolio_service.calculate_portfolio_metrics(portfolio)
        
        return {
            "portfolio_id": portfolio.id,
            "user_id": user_id,
            "nav": portfolio.nav,
            "cash_balance": portfolio.cash_balance,
            "total_positions": len(portfolio.positions),
            "metrics": {
                "total_delta": metrics.total_delta,
                "total_gamma": metrics.total_gamma,
                "total_theta": metrics.total_theta,
                "total_vega": metrics.total_vega,
                "total_rho": metrics.total_rho,
                "portfolio_beta": metrics.portfolio_beta,
                "sharpe_ratio": metrics.sharpe_ratio,
                "max_drawdown": metrics.max_drawdown,
                "win_rate": metrics.win_rate,
                "profit_factor": metrics.profit_factor
            },
            "last_updated": portfolio.last_updated.isoformat()
        }
    
    except Exception as e:
        logger.error(f"Error getting portfolio overview: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/positions", response_model=List[Dict[str, Any]])
async def get_portfolio_positions(
    user_id: str = Depends(get_current_user),
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    portfolio_service: PortfolioService = Depends()
):
    """Get all portfolio positions with optional symbol filter."""
    try:
        portfolio = await portfolio_service.get_user_portfolio(user_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        positions = portfolio.positions
        
        if symbol:
            validate_symbol(symbol)
            positions = [pos for pos in positions if pos.symbol.upper() == symbol.upper()]
        
        position_data = []
        for position in positions:
            position_data.append({
                "symbol": position.symbol,
                "quantity": position.quantity,
                "average_cost": position.average_cost,
                "current_price": position.current_price,
                "market_value": position.market_value,
                "unrealized_pnl": position.unrealized_pnl,
                "unrealized_pnl_percent": position.unrealized_pnl_percent,
                "position_type": position.position_type,
                "sector": position.sector,
                "delta": position.delta,
                "gamma": position.gamma,
                "theta": position.theta,
                "vega": position.vega,
                "rho": position.rho,
                "last_updated": position.last_updated.isoformat()
            })
        
        return position_data
    
    except Exception as e:
        logger.error(f"Error getting portfolio positions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/metrics", response_model=Dict[str, Any])
async def get_portfolio_metrics(
    user_id: str = Depends(get_current_user),
    portfolio_service: PortfolioService = Depends(),
    analytics_service: AnalyticsService = Depends()
):
    """Get detailed portfolio risk metrics and analytics."""
    try:
        portfolio = await portfolio_service.get_user_portfolio(user_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        metrics = await portfolio_service.calculate_portfolio_metrics(portfolio)
        risk_metrics = await analytics_service.calculate_portfolio_risk(portfolio)
        
        return {
            "basic_metrics": {
                "nav": portfolio.nav,
                "total_delta": metrics.total_delta,
                "total_gamma": metrics.total_gamma,
                "total_theta": metrics.total_theta,
                "total_vega": metrics.total_vega,
                "total_rho": metrics.total_rho,
                "portfolio_beta": metrics.portfolio_beta
            },
            "performance_metrics": {
                "total_return": metrics.total_return,
                "total_return_percent": metrics.total_return_percent,
                "sharpe_ratio": metrics.sharpe_ratio,
                "sortino_ratio": metrics.sortino_ratio,
                "max_drawdown": metrics.max_drawdown,
                "win_rate": metrics.win_rate,
                "profit_factor": metrics.profit_factor,
                "average_win": metrics.average_win,
                "average_loss": metrics.average_loss
            },
            "risk_metrics": {
                "var_95": risk_metrics.get("var_95"),
                "cvar_95": risk_metrics.get("cvar_95"),
                "portfolio_volatility": risk_metrics.get("portfolio_volatility"),
                "correlation_matrix": risk_metrics.get("correlation_matrix"),
                "sector_exposure": risk_metrics.get("sector_exposure"),
                "concentration_risk": risk_metrics.get("concentration_risk")
            },
            "calculated_at": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"Error calculating portfolio metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/trades", response_model=List[Dict[str, Any]])
async def get_portfolio_trades(
    user_id: str = Depends(get_current_user),
    status: Optional[TradeStatus] = Query(None, description="Filter by trade status"),
    limit: int = Query(50, ge=1, le=500, description="Number of trades to return"),
    portfolio_service: PortfolioService = Depends()
):
    """Get portfolio trades with optional status filter."""
    try:
        trades = await portfolio_service.get_user_trades(
            user_id=user_id,
            status=status,
            limit=limit
        )
        
        trade_data = []
        for trade in trades:
            trade_data.append({
                "trade_id": trade.id,
                "symbol": trade.symbol,
                "strategy": trade.strategy,
                "status": trade.status,
                "entry_date": trade.entry_date.isoformat() if trade.entry_date else None,
                "exit_date": trade.exit_date.isoformat() if trade.exit_date else None,
                "quantity": trade.quantity,
                "entry_price": trade.entry_price,
                "exit_price": trade.exit_price,
                "realized_pnl": trade.realized_pnl,
                "unrealized_pnl": trade.unrealized_pnl,
                "max_profit": trade.max_profit,
                "max_loss": trade.max_loss,
                "probability_of_profit": trade.probability_of_profit,
                "delta": trade.delta,
                "gamma": trade.gamma,
                "theta": trade.theta,
                "vega": trade.vega,
                "rho": trade.rho,
                "legs": [
                    {
                        "option_type": leg.option_type,
                        "strike": leg.strike,
                        "expiration": leg.expiration.isoformat(),
                        "quantity": leg.quantity,
                        "action": leg.action,
                        "price": leg.price
                    }
                    for leg in trade.legs
                ],
                "created_at": trade.created_at.isoformat(),
                "updated_at": trade.updated_at.isoformat()
            })
        
        return trade_data
    
    except Exception as e:
        logger.error(f"Error getting portfolio trades: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/performance", response_model=Dict[str, Any])
async def get_portfolio_performance(
    user_id: str = Depends(get_current_user),
    period: str = Query("1M", regex="^(1D|1W|1M|3M|6M|1Y|YTD|ALL)$"),
    portfolio_service: PortfolioService = Depends()
):
    """Get portfolio performance over specified period."""
    try:
        performance_data = await portfolio_service.get_portfolio_performance(
            user_id=user_id,
            period=period
        )
        
        return {
            "period": period,
            "start_date": performance_data["start_date"].isoformat(),
            "end_date": performance_data["end_date"].isoformat(),
            "starting_nav": performance_data["starting_nav"],
            "ending_nav": performance_data["ending_nav"],
            "total_return": performance_data["total_return"],
            "total_return_percent": performance_data["total_return_percent"],
            "daily_returns": performance_data["daily_returns"],
            "cumulative_returns": performance_data["cumulative_returns"],
            "volatility": performance_data["volatility"],
            "sharpe_ratio": performance_data["sharpe_ratio"],
            "max_drawdown": performance_data["max_drawdown"],
            "benchmark_comparison": performance_data.get("benchmark_comparison"),
            "calculated_at": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"Error getting portfolio performance: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/rebalance")
async def rebalance_portfolio(
    user_id: str = Depends(get_current_user),
    portfolio_service: PortfolioService = Depends(),
    analytics_service: AnalyticsService = Depends()
):
    """Trigger portfolio rebalancing based on current market conditions."""
    try:
        portfolio = await portfolio_service.get_user_portfolio(user_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        # Generate rebalancing recommendations
        recommendations = await analytics_service.generate_rebalancing_recommendations(portfolio)
        
        if not recommendations:
            return {
                "message": "No rebalancing needed",
                "current_allocation": await portfolio_service.get_portfolio_allocation(portfolio),
                "timestamp": datetime.now().isoformat()
            }
        
        # Execute rebalancing (in a real system, this might require user confirmation)
        rebalance_result = await portfolio_service.execute_rebalancing(
            portfolio=portfolio,
            recommendations=recommendations
        )
        
        return {
            "message": "Portfolio rebalancing completed",
            "recommendations_executed": len(recommendations),
            "new_allocation": rebalance_result["new_allocation"],
            "trades_executed": rebalance_result["trades_executed"],
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"Error rebalancing portfolio: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/allocation", response_model=Dict[str, Any])
async def get_portfolio_allocation(
    user_id: str = Depends(get_current_user),
    portfolio_service: PortfolioService = Depends()
):
    """Get current portfolio allocation by sector, asset class, and strategy."""
    try:
        portfolio = await portfolio_service.get_user_portfolio(user_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        allocation = await portfolio_service.get_portfolio_allocation(portfolio)
        
        return {
            "total_nav": portfolio.nav,
            "cash_percentage": (portfolio.cash_balance / portfolio.nav) * 100,
            "sector_allocation": allocation["sector_allocation"],
            "strategy_allocation": allocation["strategy_allocation"],
            "asset_class_allocation": allocation["asset_class_allocation"],
            "top_holdings": allocation["top_holdings"],
            "concentration_metrics": allocation["concentration_metrics"],
            "calculated_at": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"Error getting portfolio allocation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
