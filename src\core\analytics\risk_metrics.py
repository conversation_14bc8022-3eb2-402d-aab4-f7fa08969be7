"""
Risk Metrics Calculator for QuantEdgeFlow
Comprehensive risk analysis including VaR, CVaR, and portfolio risk metrics
"""

import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from scipy import stats
from scipy.optimize import minimize
import warnings

from src.utils.constants import PERFORMANCE_THRESHOLDS


logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


class RiskMetricsCalculator:
    """Advanced risk metrics calculation engine."""
    
    def __init__(self):
        self.logger = logger
        self.trading_days_per_year = 252
        self.confidence_levels = [0.95, 0.99]
    
    def calculate_var(
        self,
        returns: np.ndarray,
        confidence_level: float = 0.95,
        method: str = "historical"
    ) -> float:
        """
        Calculate Value at Risk (VaR).
        
        Args:
            returns: Array of portfolio returns
            confidence_level: Confidence level (e.g., 0.95 for 95%)
            method: 'historical', 'parametric', or 'monte_carlo'
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            if method == "historical":
                return self._historical_var(returns, confidence_level)
            elif method == "parametric":
                return self._parametric_var(returns, confidence_level)
            elif method == "monte_carlo":
                return self._monte_carlo_var(returns, confidence_level)
            else:
                raise ValueError(f"Unknown VaR method: {method}")
                
        except Exception as e:
            self.logger.error(f"Failed to calculate VaR: {e}")
            return 0.0
    
    def calculate_cvar(
        self,
        returns: np.ndarray,
        confidence_level: float = 0.95,
        method: str = "historical"
    ) -> float:
        """
        Calculate Conditional Value at Risk (CVaR/Expected Shortfall).
        
        Args:
            returns: Array of portfolio returns
            confidence_level: Confidence level (e.g., 0.95 for 95%)
            method: 'historical', 'parametric', or 'monte_carlo'
        """
        try:
            if len(returns) == 0:
                return 0.0
            
            var = self.calculate_var(returns, confidence_level, method)
            
            if method == "historical":
                # CVaR is the mean of returns below VaR
                tail_returns = returns[returns <= -var]
                return np.mean(tail_returns) if len(tail_returns) > 0 else var
            
            elif method == "parametric":
                # Parametric CVaR for normal distribution
                alpha = 1 - confidence_level
                mean = np.mean(returns)
                std = np.std(returns)
                
                z_alpha = stats.norm.ppf(alpha)
                cvar = mean - std * stats.norm.pdf(z_alpha) / alpha
                return -cvar  # Return as positive value
            
            else:
                # For Monte Carlo, use historical method on simulated returns
                return self.calculate_cvar(returns, confidence_level, "historical")
                
        except Exception as e:
            self.logger.error(f"Failed to calculate CVaR: {e}")
            return 0.0
    
    def calculate_maximum_drawdown(self, returns: np.ndarray) -> Dict[str, float]:
        """Calculate maximum drawdown and related metrics."""
        try:
            if len(returns) == 0:
                return {"max_drawdown": 0.0, "drawdown_duration": 0, "recovery_time": 0}
            
            # Calculate cumulative returns
            cumulative_returns = np.cumprod(1 + returns)
            
            # Calculate running maximum
            running_max = np.maximum.accumulate(cumulative_returns)
            
            # Calculate drawdown
            drawdown = (cumulative_returns - running_max) / running_max
            
            # Find maximum drawdown
            max_drawdown = np.min(drawdown)
            max_dd_index = np.argmin(drawdown)
            
            # Calculate drawdown duration (simplified)
            drawdown_start = 0
            for i in range(max_dd_index, -1, -1):
                if drawdown[i] == 0:
                    drawdown_start = i
                    break
            
            drawdown_duration = max_dd_index - drawdown_start
            
            # Calculate recovery time
            recovery_time = 0
            for i in range(max_dd_index, len(drawdown)):
                if drawdown[i] >= 0:
                    recovery_time = i - max_dd_index
                    break
            
            return {
                "max_drawdown": abs(max_drawdown),
                "drawdown_duration": drawdown_duration,
                "recovery_time": recovery_time,
                "current_drawdown": abs(drawdown[-1]) if len(drawdown) > 0 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate maximum drawdown: {e}")
            return {"max_drawdown": 0.0, "drawdown_duration": 0, "recovery_time": 0}
    
    def calculate_sharpe_ratio(
        self,
        returns: np.ndarray,
        risk_free_rate: float = 0.02
    ) -> float:
        """Calculate Sharpe ratio."""
        try:
            if len(returns) == 0:
                return 0.0
            
            excess_returns = returns - risk_free_rate / self.trading_days_per_year
            
            if np.std(excess_returns) == 0:
                return 0.0
            
            sharpe = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(self.trading_days_per_year)
            return sharpe
            
        except Exception as e:
            self.logger.error(f"Failed to calculate Sharpe ratio: {e}")
            return 0.0
    
    def calculate_sortino_ratio(
        self,
        returns: np.ndarray,
        risk_free_rate: float = 0.02,
        target_return: Optional[float] = None
    ) -> float:
        """Calculate Sortino ratio (downside deviation)."""
        try:
            if len(returns) == 0:
                return 0.0
            
            if target_return is None:
                target_return = risk_free_rate / self.trading_days_per_year
            
            excess_returns = returns - target_return
            downside_returns = excess_returns[excess_returns < 0]
            
            if len(downside_returns) == 0:
                return float('inf') if np.mean(excess_returns) > 0 else 0.0
            
            downside_deviation = np.std(downside_returns) * np.sqrt(self.trading_days_per_year)
            
            if downside_deviation == 0:
                return 0.0
            
            sortino = np.mean(excess_returns) * np.sqrt(self.trading_days_per_year) / downside_deviation
            return sortino
            
        except Exception as e:
            self.logger.error(f"Failed to calculate Sortino ratio: {e}")
            return 0.0
    
    def calculate_calmar_ratio(self, returns: np.ndarray) -> float:
        """Calculate Calmar ratio (annual return / max drawdown)."""
        try:
            if len(returns) == 0:
                return 0.0
            
            annual_return = np.mean(returns) * self.trading_days_per_year
            max_drawdown_info = self.calculate_maximum_drawdown(returns)
            max_drawdown = max_drawdown_info["max_drawdown"]
            
            if max_drawdown == 0:
                return float('inf') if annual_return > 0 else 0.0
            
            return annual_return / max_drawdown
            
        except Exception as e:
            self.logger.error(f"Failed to calculate Calmar ratio: {e}")
            return 0.0
    
    def calculate_beta(
        self,
        portfolio_returns: np.ndarray,
        market_returns: np.ndarray
    ) -> float:
        """Calculate portfolio beta relative to market."""
        try:
            if len(portfolio_returns) != len(market_returns) or len(portfolio_returns) == 0:
                return 1.0  # Default beta
            
            covariance = np.cov(portfolio_returns, market_returns)[0, 1]
            market_variance = np.var(market_returns)
            
            if market_variance == 0:
                return 1.0
            
            beta = covariance / market_variance
            return beta
            
        except Exception as e:
            self.logger.error(f"Failed to calculate beta: {e}")
            return 1.0
    
    def calculate_tracking_error(
        self,
        portfolio_returns: np.ndarray,
        benchmark_returns: np.ndarray
    ) -> float:
        """Calculate tracking error (standard deviation of excess returns)."""
        try:
            if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) == 0:
                return 0.0
            
            excess_returns = portfolio_returns - benchmark_returns
            tracking_error = np.std(excess_returns) * np.sqrt(self.trading_days_per_year)
            
            return tracking_error
            
        except Exception as e:
            self.logger.error(f"Failed to calculate tracking error: {e}")
            return 0.0
    
    def calculate_information_ratio(
        self,
        portfolio_returns: np.ndarray,
        benchmark_returns: np.ndarray
    ) -> float:
        """Calculate information ratio (excess return / tracking error)."""
        try:
            if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) == 0:
                return 0.0
            
            excess_returns = portfolio_returns - benchmark_returns
            mean_excess_return = np.mean(excess_returns) * self.trading_days_per_year
            tracking_error = self.calculate_tracking_error(portfolio_returns, benchmark_returns)
            
            if tracking_error == 0:
                return 0.0
            
            return mean_excess_return / tracking_error
            
        except Exception as e:
            self.logger.error(f"Failed to calculate information ratio: {e}")
            return 0.0
    
    def calculate_portfolio_volatility(
        self,
        weights: np.ndarray,
        covariance_matrix: np.ndarray
    ) -> float:
        """Calculate portfolio volatility given weights and covariance matrix."""
        try:
            portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))
            portfolio_volatility = np.sqrt(portfolio_variance) * np.sqrt(self.trading_days_per_year)
            
            return portfolio_volatility
            
        except Exception as e:
            self.logger.error(f"Failed to calculate portfolio volatility: {e}")
            return 0.0
    
    def calculate_risk_contribution(
        self,
        weights: np.ndarray,
        covariance_matrix: np.ndarray
    ) -> np.ndarray:
        """Calculate risk contribution of each asset to portfolio risk."""
        try:
            portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))
            
            if portfolio_variance == 0:
                return np.zeros_like(weights)
            
            marginal_risk = np.dot(covariance_matrix, weights)
            risk_contribution = weights * marginal_risk / portfolio_variance
            
            return risk_contribution
            
        except Exception as e:
            self.logger.error(f"Failed to calculate risk contribution: {e}")
            return np.zeros_like(weights)
    
    def calculate_comprehensive_risk_metrics(
        self,
        returns: np.ndarray,
        benchmark_returns: Optional[np.ndarray] = None,
        risk_free_rate: float = 0.02
    ) -> Dict[str, Any]:
        """Calculate comprehensive risk metrics for a return series."""
        try:
            if len(returns) == 0:
                return self._empty_risk_metrics()
            
            # Basic statistics
            mean_return = np.mean(returns) * self.trading_days_per_year
            volatility = np.std(returns) * np.sqrt(self.trading_days_per_year)
            
            # Risk metrics
            var_95 = self.calculate_var(returns, 0.95)
            var_99 = self.calculate_var(returns, 0.99)
            cvar_95 = self.calculate_cvar(returns, 0.95)
            cvar_99 = self.calculate_cvar(returns, 0.99)
            
            # Performance ratios
            sharpe_ratio = self.calculate_sharpe_ratio(returns, risk_free_rate)
            sortino_ratio = self.calculate_sortino_ratio(returns, risk_free_rate)
            calmar_ratio = self.calculate_calmar_ratio(returns)
            
            # Drawdown analysis
            drawdown_metrics = self.calculate_maximum_drawdown(returns)
            
            # Benchmark-relative metrics
            beta = 1.0
            tracking_error = 0.0
            information_ratio = 0.0
            
            if benchmark_returns is not None and len(benchmark_returns) == len(returns):
                beta = self.calculate_beta(returns, benchmark_returns)
                tracking_error = self.calculate_tracking_error(returns, benchmark_returns)
                information_ratio = self.calculate_information_ratio(returns, benchmark_returns)
            
            # Risk classification
            risk_level = self._classify_risk_level(volatility, drawdown_metrics["max_drawdown"])
            
            return {
                "return_metrics": {
                    "mean_annual_return": mean_return,
                    "volatility": volatility,
                    "skewness": float(stats.skew(returns)),
                    "kurtosis": float(stats.kurtosis(returns))
                },
                "risk_metrics": {
                    "var_95": var_95,
                    "var_99": var_99,
                    "cvar_95": cvar_95,
                    "cvar_99": cvar_99,
                    "max_drawdown": drawdown_metrics["max_drawdown"],
                    "current_drawdown": drawdown_metrics["current_drawdown"],
                    "drawdown_duration": drawdown_metrics["drawdown_duration"],
                    "recovery_time": drawdown_metrics["recovery_time"]
                },
                "performance_ratios": {
                    "sharpe_ratio": sharpe_ratio,
                    "sortino_ratio": sortino_ratio,
                    "calmar_ratio": calmar_ratio,
                    "information_ratio": information_ratio
                },
                "market_metrics": {
                    "beta": beta,
                    "tracking_error": tracking_error
                },
                "risk_classification": {
                    "risk_level": risk_level,
                    "volatility_percentile": self._calculate_volatility_percentile(volatility),
                    "drawdown_percentile": self._calculate_drawdown_percentile(drawdown_metrics["max_drawdown"])
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate comprehensive risk metrics: {e}")
            return self._empty_risk_metrics()
    
    def _historical_var(self, returns: np.ndarray, confidence_level: float) -> float:
        """Calculate VaR using historical method."""
        percentile = (1 - confidence_level) * 100
        var = np.percentile(returns, percentile)
        return -var  # Return as positive value
    
    def _parametric_var(self, returns: np.ndarray, confidence_level: float) -> float:
        """Calculate VaR using parametric method (normal distribution)."""
        mean = np.mean(returns)
        std = np.std(returns)
        z_score = stats.norm.ppf(1 - confidence_level)
        var = mean + z_score * std
        return -var  # Return as positive value
    
    def _monte_carlo_var(
        self,
        returns: np.ndarray,
        confidence_level: float,
        num_simulations: int = 10000
    ) -> float:
        """Calculate VaR using Monte Carlo simulation."""
        mean = np.mean(returns)
        std = np.std(returns)
        
        # Generate random returns
        simulated_returns = np.random.normal(mean, std, num_simulations)
        
        # Calculate VaR from simulated returns
        return self._historical_var(simulated_returns, confidence_level)
    
    def _classify_risk_level(self, volatility: float, max_drawdown: float) -> str:
        """Classify risk level based on volatility and drawdown."""
        if volatility < 0.10 and max_drawdown < 0.05:
            return "LOW"
        elif volatility < 0.20 and max_drawdown < 0.15:
            return "MODERATE"
        elif volatility < 0.35 and max_drawdown < 0.25:
            return "HIGH"
        else:
            return "EXTREME"
    
    def _calculate_volatility_percentile(self, volatility: float) -> float:
        """Calculate volatility percentile (simplified)."""
        # This would typically use historical market data
        # For now, use rough estimates
        if volatility < 0.10:
            return 25.0
        elif volatility < 0.20:
            return 50.0
        elif volatility < 0.30:
            return 75.0
        else:
            return 95.0
    
    def _calculate_drawdown_percentile(self, max_drawdown: float) -> float:
        """Calculate drawdown percentile (simplified)."""
        # This would typically use historical market data
        if max_drawdown < 0.05:
            return 25.0
        elif max_drawdown < 0.15:
            return 50.0
        elif max_drawdown < 0.25:
            return 75.0
        else:
            return 95.0
    
    def _empty_risk_metrics(self) -> Dict[str, Any]:
        """Return empty risk metrics structure."""
        return {
            "return_metrics": {
                "mean_annual_return": 0.0,
                "volatility": 0.0,
                "skewness": 0.0,
                "kurtosis": 0.0
            },
            "risk_metrics": {
                "var_95": 0.0,
                "var_99": 0.0,
                "cvar_95": 0.0,
                "cvar_99": 0.0,
                "max_drawdown": 0.0,
                "current_drawdown": 0.0,
                "drawdown_duration": 0,
                "recovery_time": 0
            },
            "performance_ratios": {
                "sharpe_ratio": 0.0,
                "sortino_ratio": 0.0,
                "calmar_ratio": 0.0,
                "information_ratio": 0.0
            },
            "market_metrics": {
                "beta": 1.0,
                "tracking_error": 0.0
            },
            "risk_classification": {
                "risk_level": "UNKNOWN",
                "volatility_percentile": 0.0,
                "drawdown_percentile": 0.0
            }
        }


# Global risk metrics calculator instance
risk_calculator = RiskMetricsCalculator()
