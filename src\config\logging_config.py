"""
Logging Configuration for QuantEdgeFlow
Advanced logging setup with structured logging, multiple handlers, and monitoring
"""

import logging
import logging.config
import sys
import os
from typing import Dict, Any
from pathlib import Path
import structlog
from datetime import datetime

from config.settings import get_settings


settings = get_settings()


def setup_logging() -> None:
    """Configure comprehensive logging for the application."""
    
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Logging configuration dictionary
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "simple": {
                "format": "%(levelname)s - %(name)s - %(message)s"
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(funcName)s %(lineno)d %(message)s"
            },
            "structured": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.dev.ConsoleRenderer(colors=True),
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "structured" if settings.DEBUG else "simple",
                "stream": sys.stdout
            },
            "file_detailed": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "formatter": "detailed",
                "filename": "logs/quantedgeflow.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            },
            "file_json": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/quantedgeflow.json",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": "logs/errors.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8"
            },
            "trading_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/trading.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 20,
                "encoding": "utf8"
            },
            "performance_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/performance.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8"
            }
        },
        "loggers": {
            "": {  # Root logger
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file_detailed", "file_json", "error_file"],
                "propagate": False
            },
            "quantedgeflow": {
                "level": "DEBUG",
                "handlers": ["console", "file_detailed", "file_json"],
                "propagate": False
            },
            "quantedgeflow.trading": {
                "level": "INFO",
                "handlers": ["trading_file", "console"],
                "propagate": False
            },
            "quantedgeflow.performance": {
                "level": "INFO",
                "handlers": ["performance_file", "console"],
                "propagate": False
            },
            "quantedgeflow.analytics": {
                "level": "DEBUG",
                "handlers": ["file_detailed", "console"],
                "propagate": False
            },
            "quantedgeflow.data": {
                "level": "INFO",
                "handlers": ["file_detailed", "console"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console", "file_detailed"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["file_detailed"],
                "propagate": False
            },
            "sqlalchemy": {
                "level": "WARNING",
                "handlers": ["file_detailed"],
                "propagate": False
            },
            "sqlalchemy.engine": {
                "level": "INFO" if settings.DEBUG else "WARNING",
                "handlers": ["file_detailed"],
                "propagate": False
            }
        }
    }
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)
    
    # Set up Sentry if configured
    if settings.SENTRY_DSN:
        try:
            import sentry_sdk
            from sentry_sdk.integrations.logging import LoggingIntegration
            from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
            from sentry_sdk.integrations.asyncio import AsyncioIntegration
            
            sentry_logging = LoggingIntegration(
                level=logging.INFO,
                event_level=logging.ERROR
            )
            
            sentry_sdk.init(
                dsn=settings.SENTRY_DSN,
                integrations=[
                    sentry_logging,
                    SqlalchemyIntegration(),
                    AsyncioIntegration(auto_enabling_integrations=False)
                ],
                traces_sample_rate=0.1,
                environment="production" if not settings.DEBUG else "development",
                release=settings.VERSION
            )
            
            logging.getLogger(__name__).info("Sentry logging integration enabled")
            
        except ImportError:
            logging.getLogger(__name__).warning("Sentry SDK not available")
    
    # Log startup information
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging system initialized",
        extra={
            "log_level": settings.LOG_LEVEL,
            "debug_mode": settings.DEBUG,
            "sentry_enabled": bool(settings.SENTRY_DSN),
            "log_directory": str(log_dir.absolute())
        }
    )


class PerformanceLogger:
    """Specialized logger for performance metrics and trading activities."""
    
    def __init__(self):
        self.logger = logging.getLogger("quantedgeflow.performance")
        self.trading_logger = logging.getLogger("quantedgeflow.trading")
    
    def log_trade_execution(self, trade_data: Dict[str, Any]) -> None:
        """Log trade execution details."""
        self.trading_logger.info(
            "Trade executed",
            extra={
                "event_type": "trade_execution",
                "timestamp": datetime.utcnow().isoformat(),
                **trade_data
            }
        )
    
    def log_portfolio_update(self, portfolio_data: Dict[str, Any]) -> None:
        """Log portfolio state changes."""
        self.trading_logger.info(
            "Portfolio updated",
            extra={
                "event_type": "portfolio_update",
                "timestamp": datetime.utcnow().isoformat(),
                **portfolio_data
            }
        )
    
    def log_risk_metrics(self, risk_data: Dict[str, Any]) -> None:
        """Log risk metrics calculation."""
        self.logger.info(
            "Risk metrics calculated",
            extra={
                "event_type": "risk_calculation",
                "timestamp": datetime.utcnow().isoformat(),
                **risk_data
            }
        )
    
    def log_performance_metrics(self, performance_data: Dict[str, Any]) -> None:
        """Log performance metrics."""
        self.logger.info(
            "Performance metrics updated",
            extra={
                "event_type": "performance_update",
                "timestamp": datetime.utcnow().isoformat(),
                **performance_data
            }
        )
    
    def log_api_performance(self, endpoint: str, duration: float, status_code: int) -> None:
        """Log API endpoint performance."""
        self.logger.info(
            "API request completed",
            extra={
                "event_type": "api_performance",
                "endpoint": endpoint,
                "duration_ms": round(duration * 1000, 2),
                "status_code": status_code,
                "timestamp": datetime.utcnow().isoformat()
            }
        )


# Global performance logger instance
performance_logger = PerformanceLogger()


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name."""
    return logging.getLogger(f"quantedgeflow.{name}")


def log_function_call(func_name: str, args: Dict[str, Any] = None, duration: float = None):
    """Decorator for logging function calls with performance metrics."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(func.__module__)
            start_time = datetime.utcnow()
            
            try:
                result = func(*args, **kwargs)
                end_time = datetime.utcnow()
                duration = (end_time - start_time).total_seconds()
                
                logger.debug(
                    f"Function {func_name} completed successfully",
                    extra={
                        "function": func_name,
                        "duration_ms": round(duration * 1000, 2),
                        "status": "success"
                    }
                )
                
                return result
                
            except Exception as e:
                end_time = datetime.utcnow()
                duration = (end_time - start_time).total_seconds()
                
                logger.error(
                    f"Function {func_name} failed",
                    extra={
                        "function": func_name,
                        "duration_ms": round(duration * 1000, 2),
                        "status": "error",
                        "error": str(e)
                    },
                    exc_info=True
                )
                
                raise
        
        return wrapper
    return decorator
