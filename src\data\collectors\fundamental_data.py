"""
Fundamental Data Collector for QuantEdgeFlow
Comprehensive fundamental analysis data collection and processing
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date, timedelta
from decimal import Decimal
import aiohttp
import yfinance as yf
from alpha_vantage.fundamentaldata import FundamentalData
import pandas as pd

from config.settings import get_settings
from config.database import get_async_session
from core.models.market_data import EconomicIndicator, DataSource
from utils.logger import get_performance_logger

settings = get_settings()
logger = logging.getLogger(__name__)
performance_logger = get_performance_logger()


class FundamentalDataCollector:
    """Comprehensive fundamental data collector."""
    
    def __init__(self):
        self.logger = logger
        self.alpha_vantage_api_key = settings.ALPHA_VANTAGE_API_KEY
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Initialize Alpha Vantage client
        if self.alpha_vantage_api_key:
            self.av_fundamental = FundamentalData(key=self.alpha_vantage_api_key, output_format='pandas')
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def collect_company_overview(self, symbol: str) -> Dict[str, Any]:
        """Collect comprehensive company overview data."""
        try:
            start_time = datetime.utcnow()
            
            # Get data from multiple sources
            yf_data = await self._get_yfinance_fundamentals(symbol)
            av_data = await self._get_alpha_vantage_overview(symbol)
            
            # Combine and normalize data
            combined_data = self._combine_fundamental_data(yf_data, av_data)
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.log_api_performance(
                f"fundamental_overview_{symbol}", duration, 200
            )
            
            return combined_data
            
        except Exception as e:
            self.logger.error(f"Failed to collect company overview for {symbol}: {e}")
            return {}
    
    async def collect_financial_statements(self, symbol: str) -> Dict[str, pd.DataFrame]:
        """Collect financial statements (income, balance sheet, cash flow)."""
        try:
            start_time = datetime.utcnow()
            
            # Get financial statements from yfinance
            ticker = yf.Ticker(symbol)
            
            statements = {}
            
            # Income statement
            try:
                income_stmt = ticker.financials
                if not income_stmt.empty:
                    statements['income_statement'] = income_stmt
            except Exception as e:
                self.logger.warning(f"Failed to get income statement for {symbol}: {e}")
            
            # Balance sheet
            try:
                balance_sheet = ticker.balance_sheet
                if not balance_sheet.empty:
                    statements['balance_sheet'] = balance_sheet
            except Exception as e:
                self.logger.warning(f"Failed to get balance sheet for {symbol}: {e}")
            
            # Cash flow statement
            try:
                cash_flow = ticker.cashflow
                if not cash_flow.empty:
                    statements['cash_flow'] = cash_flow
            except Exception as e:
                self.logger.warning(f"Failed to get cash flow for {symbol}: {e}")
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.log_api_performance(
                f"financial_statements_{symbol}", duration, 200
            )
            
            return statements
            
        except Exception as e:
            self.logger.error(f"Failed to collect financial statements for {symbol}: {e}")
            return {}
    
    async def collect_earnings_data(self, symbol: str) -> Dict[str, Any]:
        """Collect earnings data and estimates."""
        try:
            start_time = datetime.utcnow()
            
            ticker = yf.Ticker(symbol)
            
            earnings_data = {}
            
            # Historical earnings
            try:
                earnings = ticker.earnings
                if not earnings.empty:
                    earnings_data['historical_earnings'] = earnings.to_dict()
            except Exception as e:
                self.logger.warning(f"Failed to get historical earnings for {symbol}: {e}")
            
            # Quarterly earnings
            try:
                quarterly_earnings = ticker.quarterly_earnings
                if not quarterly_earnings.empty:
                    earnings_data['quarterly_earnings'] = quarterly_earnings.to_dict()
            except Exception as e:
                self.logger.warning(f"Failed to get quarterly earnings for {symbol}: {e}")
            
            # Earnings calendar
            try:
                calendar = ticker.calendar
                if calendar is not None and not calendar.empty:
                    earnings_data['earnings_calendar'] = calendar.to_dict()
            except Exception as e:
                self.logger.warning(f"Failed to get earnings calendar for {symbol}: {e}")
            
            # Alpha Vantage earnings data
            if self.alpha_vantage_api_key:
                try:
                    av_earnings, _ = self.av_fundamental.get_company_earnings(symbol)
                    if not av_earnings.empty:
                        earnings_data['av_earnings'] = av_earnings.to_dict()
                except Exception as e:
                    self.logger.warning(f"Failed to get Alpha Vantage earnings for {symbol}: {e}")
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.log_api_performance(
                f"earnings_data_{symbol}", duration, 200
            )
            
            return earnings_data
            
        except Exception as e:
            self.logger.error(f"Failed to collect earnings data for {symbol}: {e}")
            return {}
    
    async def collect_analyst_recommendations(self, symbol: str) -> Dict[str, Any]:
        """Collect analyst recommendations and price targets."""
        try:
            start_time = datetime.utcnow()
            
            ticker = yf.Ticker(symbol)
            
            recommendations_data = {}
            
            # Analyst recommendations
            try:
                recommendations = ticker.recommendations
                if recommendations is not None and not recommendations.empty:
                    recommendations_data['recommendations'] = recommendations.to_dict()
            except Exception as e:
                self.logger.warning(f"Failed to get recommendations for {symbol}: {e}")
            
            # Analyst price targets
            try:
                info = ticker.info
                if info:
                    price_targets = {
                        'target_high_price': info.get('targetHighPrice'),
                        'target_low_price': info.get('targetLowPrice'),
                        'target_mean_price': info.get('targetMeanPrice'),
                        'target_median_price': info.get('targetMedianPrice'),
                        'recommendation_mean': info.get('recommendationMean'),
                        'recommendation_key': info.get('recommendationKey'),
                        'number_of_analyst_opinions': info.get('numberOfAnalystOpinions')
                    }
                    recommendations_data['price_targets'] = price_targets
            except Exception as e:
                self.logger.warning(f"Failed to get price targets for {symbol}: {e}")
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.log_api_performance(
                f"analyst_recommendations_{symbol}", duration, 200
            )
            
            return recommendations_data
            
        except Exception as e:
            self.logger.error(f"Failed to collect analyst recommendations for {symbol}: {e}")
            return {}
    
    async def _get_yfinance_fundamentals(self, symbol: str) -> Dict[str, Any]:
        """Get fundamental data from Yahoo Finance."""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info:
                return {}
            
            # Extract key fundamental metrics
            fundamentals = {
                'market_cap': info.get('marketCap'),
                'enterprise_value': info.get('enterpriseValue'),
                'pe_ratio': info.get('trailingPE'),
                'forward_pe': info.get('forwardPE'),
                'peg_ratio': info.get('pegRatio'),
                'price_to_book': info.get('priceToBook'),
                'price_to_sales': info.get('priceToSalesTrailing12Months'),
                'enterprise_to_revenue': info.get('enterpriseToRevenue'),
                'enterprise_to_ebitda': info.get('enterpriseToEbitda'),
                'profit_margins': info.get('profitMargins'),
                'operating_margins': info.get('operatingMargins'),
                'return_on_assets': info.get('returnOnAssets'),
                'return_on_equity': info.get('returnOnEquity'),
                'revenue_growth': info.get('revenueGrowth'),
                'earnings_growth': info.get('earningsGrowth'),
                'debt_to_equity': info.get('debtToEquity'),
                'current_ratio': info.get('currentRatio'),
                'quick_ratio': info.get('quickRatio'),
                'dividend_yield': info.get('dividendYield'),
                'payout_ratio': info.get('payoutRatio'),
                'beta': info.get('beta'),
                'shares_outstanding': info.get('sharesOutstanding'),
                'float_shares': info.get('floatShares'),
                'shares_short': info.get('sharesShort'),
                'short_ratio': info.get('shortRatio'),
                'book_value': info.get('bookValue'),
                'price_to_book': info.get('priceToBook'),
                'last_fiscal_year_end': info.get('lastFiscalYearEnd'),
                'next_fiscal_year_end': info.get('nextFiscalYearEnd'),
                'most_recent_quarter': info.get('mostRecentQuarter'),
                'earnings_quarterly_growth': info.get('earningsQuarterlyGrowth'),
                'revenue_quarterly_growth': info.get('revenueQuarterlyGrowth')
            }
            
            # Remove None values
            fundamentals = {k: v for k, v in fundamentals.items() if v is not None}
            
            return fundamentals
            
        except Exception as e:
            self.logger.error(f"Failed to get Yahoo Finance fundamentals for {symbol}: {e}")
            return {}
    
    async def _get_alpha_vantage_overview(self, symbol: str) -> Dict[str, Any]:
        """Get company overview from Alpha Vantage."""
        try:
            if not self.alpha_vantage_api_key:
                return {}
            
            overview, _ = self.av_fundamental.get_company_overview(symbol)
            
            if overview.empty:
                return {}
            
            # Convert to dictionary
            overview_dict = overview.to_dict()
            
            # Extract first row (company data)
            if overview_dict:
                first_key = list(overview_dict.keys())[0]
                return {k: v[first_key] for k, v in overview_dict.items()}
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Failed to get Alpha Vantage overview for {symbol}: {e}")
            return {}
    
    def _combine_fundamental_data(self, yf_data: Dict[str, Any], av_data: Dict[str, Any]) -> Dict[str, Any]:
        """Combine fundamental data from multiple sources."""
        combined = {}
        
        # Start with Yahoo Finance data
        combined.update(yf_data)
        
        # Add Alpha Vantage data (prefer YF for conflicts)
        for key, value in av_data.items():
            if key not in combined and value is not None:
                combined[key] = value
        
        # Add metadata
        combined['data_sources'] = []
        if yf_data:
            combined['data_sources'].append('yahoo_finance')
        if av_data:
            combined['data_sources'].append('alpha_vantage')
        
        combined['last_updated'] = datetime.utcnow().isoformat()
        
        return combined


# Global fundamental data collector instance
fundamental_data_collector = FundamentalDataCollector()
