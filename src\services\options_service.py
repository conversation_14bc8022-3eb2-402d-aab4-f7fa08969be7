"""
Options Service for QuantEdgeFlow
Business logic for options analysis, pricing, and chain management
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid
import asyncio

from sqlalchemy import select, update, delete, and_, or_, desc
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession

from config.database import get_async_session
from core.models.options import (
    OptionContract, OptionChain, VolatilitySurface, OptionType,
    OptionContractResponse, OptionChainResponse, OptionChainRequest,
    GreeksCalculationRequest, GreeksResponse, VolatilitySurfaceResponse
)
from core.models.market_data import StockQuote
from core.algorithms.black_scholes import BlackScholesCalculator, OptionParameters
from data.collectors.options_chain import OptionsChainCollector
from utils.logger import get_performance_logger


logger = logging.getLogger(__name__)
performance_logger = get_performance_logger()


class OptionsService:
    """Options analysis and management service."""
    
    def __init__(self):
        self.logger = logger
        self.bs_calculator = BlackScholesCalculator()
        self.options_collector = OptionsChainCollector()
    
    async def get_option_chain(
        self, 
        request: OptionChainRequest
    ) -> Optional[OptionChainResponse]:
        """Get option chain for a symbol."""
        async with get_async_session() as session:
            try:
                start_time = datetime.utcnow()
                
                # Build query conditions
                conditions = [OptionContract.symbol == request.symbol.upper()]
                
                if request.expiration_date:
                    conditions.append(OptionContract.expiration_date == request.expiration_date)
                
                if request.min_days_to_expiration is not None:
                    conditions.append(OptionContract.days_to_expiration >= request.min_days_to_expiration)
                
                if request.max_days_to_expiration is not None:
                    conditions.append(OptionContract.days_to_expiration <= request.max_days_to_expiration)
                
                if request.min_strike is not None:
                    conditions.append(OptionContract.strike_price >= request.min_strike)
                
                if request.max_strike is not None:
                    conditions.append(OptionContract.strike_price <= request.max_strike)
                
                if request.option_type and request.option_type != "both":
                    conditions.append(OptionContract.option_type == request.option_type)
                
                if request.min_volume is not None:
                    conditions.append(OptionContract.volume >= request.min_volume)
                
                if request.min_open_interest is not None:
                    conditions.append(OptionContract.open_interest >= request.min_open_interest)
                
                # Execute query
                stmt = select(OptionContract).where(and_(*conditions)).order_by(
                    OptionContract.expiration_date,
                    OptionContract.strike_price
                )
                
                result = await session.execute(stmt)
                contracts = result.scalars().all()
                
                if not contracts:
                    # Try to fetch fresh data
                    await self._refresh_option_chain(request.symbol.upper())
                    
                    # Retry query
                    result = await session.execute(stmt)
                    contracts = result.scalars().all()
                
                if not contracts:
                    return None
                
                # Get or create option chain record
                chain = await self._get_or_create_option_chain(
                    session, 
                    request.symbol.upper(), 
                    contracts[0].expiration_date if contracts else None
                )
                
                # Separate calls and puts
                calls = [c for c in contracts if c.option_type == OptionType.CALL]
                puts = [c for c in contracts if c.option_type == OptionType.PUT]
                
                # Log performance
                duration = (datetime.utcnow() - start_time).total_seconds()
                performance_logger.info(f"Option chain retrieved", extra={
                    "symbol": request.symbol,
                    "contracts_count": len(contracts),
                    "calls_count": len(calls),
                    "puts_count": len(puts),
                    "duration_seconds": duration
                })
                
                return OptionChainResponse(
                    id=chain.id,
                    symbol=chain.symbol,
                    expiration_date=chain.expiration_date,
                    underlying_price=chain.underlying_price,
                    underlying_change=chain.underlying_change,
                    underlying_change_percent=chain.underlying_change_percent,
                    total_call_volume=chain.total_call_volume,
                    total_put_volume=chain.total_put_volume,
                    total_call_open_interest=chain.total_call_open_interest,
                    total_put_open_interest=chain.total_put_open_interest,
                    put_call_ratio=chain.put_call_ratio,
                    implied_volatility_rank=chain.implied_volatility_rank,
                    implied_volatility_percentile=chain.implied_volatility_percentile,
                    historical_volatility=chain.historical_volatility,
                    max_pain=chain.max_pain,
                    gamma_exposure=chain.gamma_exposure,
                    calls=[OptionContractResponse.from_orm(c) for c in calls],
                    puts=[OptionContractResponse.from_orm(c) for c in puts],
                    created_at=chain.created_at,
                    updated_at=chain.updated_at
                )
                
            except Exception as e:
                self.logger.error(f"Failed to get option chain: {e}", extra={
                    "symbol": request.symbol
                })
                raise
    
    async def calculate_greeks(
        self, 
        request: GreeksCalculationRequest
    ) -> GreeksResponse:
        """Calculate option Greeks and theoretical price."""
        try:
            start_time = datetime.utcnow()
            
            # Get current volatility if not provided
            volatility = request.volatility
            if volatility is None:
                volatility = await self._get_implied_volatility(
                    request.symbol,
                    request.option_type,
                    request.strike_price,
                    request.expiration_date
                )
                if volatility is None:
                    volatility = 0.25  # Default 25% volatility
            
            # Calculate time to expiration
            time_to_expiration = (request.expiration_date - date.today()).days / 365.0
            
            if time_to_expiration <= 0:
                # Option has expired
                intrinsic_value = self._calculate_intrinsic_value(
                    request.underlying_price,
                    request.strike_price,
                    request.option_type
                )
                
                return GreeksResponse(
                    delta=0.0,
                    gamma=0.0,
                    theta=0.0,
                    vega=0.0,
                    rho=0.0,
                    theoretical_price=intrinsic_value,
                    intrinsic_value=intrinsic_value,
                    time_value=Decimal(0),
                    implied_volatility=volatility
                )
            
            # Create option parameters
            params = OptionParameters(
                spot_price=float(request.underlying_price),
                strike_price=float(request.strike_price),
                time_to_expiration=time_to_expiration,
                risk_free_rate=request.risk_free_rate,
                volatility=volatility,
                dividend_yield=request.dividend_yield,
                option_type=request.option_type
            )
            
            # Calculate pricing and Greeks
            pricing = self.bs_calculator.calculate_full_pricing(params)
            
            # Calculate additional metrics
            intrinsic_value = self._calculate_intrinsic_value(
                request.underlying_price,
                request.strike_price,
                request.option_type
            )
            
            time_value = pricing.price - float(intrinsic_value)
            
            # Calculate probability of profit (simplified)
            prob_profit = self._calculate_probability_of_profit(
                request.underlying_price,
                request.strike_price,
                request.option_type,
                volatility,
                time_to_expiration
            )
            
            # Calculate break-even price
            break_even = self._calculate_break_even_price(
                request.strike_price,
                Decimal(pricing.price),
                request.option_type
            )
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.info(f"Greeks calculated", extra={
                "symbol": request.symbol,
                "option_type": request.option_type,
                "strike": float(request.strike_price),
                "duration_seconds": duration
            })
            
            return GreeksResponse(
                delta=pricing.delta,
                gamma=pricing.gamma,
                theta=pricing.theta,
                vega=pricing.vega,
                rho=pricing.rho,
                theoretical_price=Decimal(pricing.price),
                intrinsic_value=intrinsic_value,
                time_value=Decimal(time_value),
                implied_volatility=volatility,
                probability_of_profit=prob_profit,
                break_even_price=break_even,
                max_profit=None,  # Would calculate based on strategy
                max_loss=None     # Would calculate based on strategy
            )
            
        except Exception as e:
            self.logger.error(f"Failed to calculate Greeks: {e}", extra={
                "symbol": request.symbol,
                "option_type": request.option_type,
                "strike": float(request.strike_price)
            })
            raise
    
    async def get_volatility_surface(
        self, 
        symbol: str, 
        calculation_date: Optional[date] = None
    ) -> Optional[VolatilitySurfaceResponse]:
        """Get volatility surface for a symbol."""
        async with get_async_session() as session:
            try:
                if calculation_date is None:
                    calculation_date = date.today()
                
                stmt = select(VolatilitySurface).where(
                    and_(
                        VolatilitySurface.symbol == symbol.upper(),
                        VolatilitySurface.calculation_date == calculation_date
                    )
                )
                
                result = await session.execute(stmt)
                surface = result.scalar_one_or_none()
                
                if not surface:
                    # Calculate volatility surface
                    surface = await self._calculate_volatility_surface(symbol.upper(), calculation_date)
                    if surface:
                        session.add(surface)
                        await session.commit()
                        await session.refresh(surface)
                
                if surface:
                    return VolatilitySurfaceResponse.from_orm(surface)
                
                return None
                
            except Exception as e:
                self.logger.error(f"Failed to get volatility surface: {e}", extra={
                    "symbol": symbol,
                    "calculation_date": calculation_date
                })
                raise
    
    async def _refresh_option_chain(self, symbol: str) -> None:
        """Refresh option chain data from external sources."""
        try:
            # This would integrate with external data providers
            # For now, we'll create a placeholder implementation
            await self.options_collector.collect_options_chain(symbol)
            
        except Exception as e:
            self.logger.warning(f"Failed to refresh option chain: {e}", extra={
                "symbol": symbol
            })
    
    async def _get_or_create_option_chain(
        self, 
        session: AsyncSession, 
        symbol: str, 
        expiration_date: Optional[date]
    ) -> OptionChain:
        """Get or create option chain record."""
        if expiration_date is None:
            expiration_date = date.today() + timedelta(days=30)  # Default to 30 days
        
        stmt = select(OptionChain).where(
            and_(
                OptionChain.symbol == symbol,
                OptionChain.expiration_date == expiration_date
            )
        )
        
        result = await session.execute(stmt)
        chain = result.scalar_one_or_none()
        
        if not chain:
            chain = OptionChain(
                symbol=symbol,
                expiration_date=expiration_date
            )
            session.add(chain)
            await session.flush()
        
        return chain
    
    async def _get_implied_volatility(
        self, 
        symbol: str, 
        option_type: str, 
        strike_price: Decimal, 
        expiration_date: date
    ) -> Optional[float]:
        """Get implied volatility for an option."""
        async with get_async_session() as session:
            try:
                stmt = select(OptionContract.implied_volatility).where(
                    and_(
                        OptionContract.symbol == symbol.upper(),
                        OptionContract.option_type == option_type,
                        OptionContract.strike_price == strike_price,
                        OptionContract.expiration_date == expiration_date
                    )
                )
                
                result = await session.execute(stmt)
                iv = result.scalar_one_or_none()
                
                return iv
                
            except Exception as e:
                self.logger.warning(f"Failed to get implied volatility: {e}")
                return None
    
    def _calculate_intrinsic_value(
        self, 
        underlying_price: Decimal, 
        strike_price: Decimal, 
        option_type: str
    ) -> Decimal:
        """Calculate intrinsic value of option."""
        if option_type == OptionType.CALL:
            return max(Decimal(0), underlying_price - strike_price)
        else:  # PUT
            return max(Decimal(0), strike_price - underlying_price)
    
    def _calculate_probability_of_profit(
        self, 
        underlying_price: Decimal, 
        strike_price: Decimal, 
        option_type: str, 
        volatility: float, 
        time_to_expiration: float
    ) -> float:
        """Calculate probability of profit (simplified)."""
        # This is a simplified calculation
        # In practice, you'd use more sophisticated models
        import math
        from scipy.stats import norm
        
        S = float(underlying_price)
        K = float(strike_price)
        sigma = volatility
        T = time_to_expiration
        
        if T <= 0:
            return 1.0 if self._calculate_intrinsic_value(underlying_price, strike_price, option_type) > 0 else 0.0
        
        d2 = (math.log(S / K) + (-0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        
        if option_type == OptionType.CALL:
            return norm.cdf(d2)
        else:  # PUT
            return norm.cdf(-d2)
    
    def _calculate_break_even_price(
        self, 
        strike_price: Decimal, 
        option_price: Decimal, 
        option_type: str
    ) -> Decimal:
        """Calculate break-even price for option."""
        if option_type == OptionType.CALL:
            return strike_price + option_price
        else:  # PUT
            return strike_price - option_price
    
    async def _calculate_volatility_surface(
        self, 
        symbol: str, 
        calculation_date: date
    ) -> Optional[VolatilitySurface]:
        """Calculate volatility surface from option contracts."""
        # This would implement volatility surface calculation
        # For now, return a placeholder
        return None
