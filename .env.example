# QuantEdgeFlow Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
DEBUG=false
HOST=0.0.0.0
PORT=8000
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-characters
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# CORS Settings (comma-separated list)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,https://yourdomain.com

# Database Configuration
DATABASE_URL=postgresql+asyncpg://quantuser:quantpass123@localhost:5432/quantedgeflow
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://:redispass123@localhost:6379/0
REDIS_POOL_SIZE=10
REDIS_PASSWORD=redispass123

# PostgreSQL Configuration
POSTGRES_PASSWORD=quantpass123
TIMESCALE_PASSWORD=timepass123

# Market Data API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
POLYGON_API_KEY=your_polygon_api_key_here
YAHOO_FINANCE_ENABLED=true

# Options Data Settings
OPTIONS_DATA_REFRESH_INTERVAL=60
MAX_OPTION_CHAINS_PER_REQUEST=50

# Trading Configuration
DEFAULT_NAV=100000.0
MAX_TRADES_PER_CYCLE=5
MIN_PROBABILITY_OF_PROFIT=0.65
MIN_CREDIT_LOSS_RATIO=0.33
MAX_LOSS_PERCENTAGE=0.005

# Portfolio Risk Limits
MAX_PORTFOLIO_DELTA=0.30
MIN_PORTFOLIO_VEGA=-0.05
MAX_TRADES_PER_SECTOR=2

# Analytics Settings
VOLATILITY_LOOKBACK_DAYS=252
RISK_FREE_RATE=0.05

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring & Error Tracking
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_ENABLED=true

# Background Tasks
MARKET_DATA_COLLECTION_ENABLED=true
PORTFOLIO_REBALANCE_INTERVAL=300

# External Services
NOTIFICATION_WEBHOOK_URL=your_webhook_url_here
SLACK_WEBHOOK_URL=your_slack_webhook_url_here

# Grafana Configuration
GRAFANA_PASSWORD=admin123

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>

# AWS Configuration (if using AWS services)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=quantedgeflow-data

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# SSL Configuration
SSL_ENABLED=false
SSL_CERT_PATH=/etc/ssl/certs/quantedgeflow.crt
SSL_KEY_PATH=/etc/ssl/private/quantedgeflow.key

# Development Settings
RELOAD_ON_CHANGE=false
ENABLE_PROFILING=false
MOCK_EXTERNAL_APIS=false

# Testing Configuration
TEST_DATABASE_URL=postgresql+asyncpg://quantuser:quantpass123@localhost:5432/quantedgeflow_test
TEST_REDIS_URL=redis://:redispass123@localhost:6379/1

# Feature Flags
ENABLE_MACHINE_LEARNING=true
ENABLE_ALTERNATIVE_DATA=true
ENABLE_REAL_TIME_TRADING=false
ENABLE_PAPER_TRADING=true

# Machine Learning Configuration
ML_MODEL_UPDATE_INTERVAL=3600  # 1 hour
ML_TRAINING_DATA_DAYS=252  # 1 year
ML_PREDICTION_HORIZON=30  # 30 days

# Alternative Data Sources
SOCIAL_SENTIMENT_ENABLED=false
NEWS_SENTIMENT_ENABLED=false
SATELLITE_DATA_ENABLED=false

# Risk Management
MAX_DAILY_LOSS=5000.0  # $5,000 max daily loss
MAX_POSITION_SIZE=0.10  # 10% max position size
STOP_LOSS_ENABLED=true
TAKE_PROFIT_ENABLED=true

# Compliance & Reporting
REGULATORY_REPORTING_ENABLED=false
AUDIT_LOG_ENABLED=true
TRADE_REPORTING_ENABLED=true

# Performance Optimization
CACHE_TTL_MARKET_DATA=60  # 1 minute
CACHE_TTL_OPTIONS_DATA=300  # 5 minutes
CACHE_TTL_FUNDAMENTAL_DATA=3600  # 1 hour
ENABLE_QUERY_OPTIMIZATION=true

# Webhook Configuration
WEBHOOK_TIMEOUT=30
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=5

# Time Zone Configuration
TIMEZONE=America/New_York
MARKET_TIMEZONE=America/New_York

# Data Retention Policies
MARKET_DATA_RETENTION_DAYS=365
TRADE_DATA_RETENTION_DAYS=2555  # 7 years
LOG_RETENTION_DAYS=90

# API Configuration
API_VERSION=v1
MAX_REQUEST_SIZE=10485760  # 10MB
REQUEST_TIMEOUT=30

# Security Configuration
ENABLE_2FA=false
SESSION_TIMEOUT=3600  # 1 hour
PASSWORD_MIN_LENGTH=8
REQUIRE_STRONG_PASSWORDS=true

# Notification Settings
EMAIL_NOTIFICATIONS_ENABLED=true
SLACK_NOTIFICATIONS_ENABLED=false
WEBHOOK_NOTIFICATIONS_ENABLED=false
NOTIFICATION_COOLDOWN=300  # 5 minutes
