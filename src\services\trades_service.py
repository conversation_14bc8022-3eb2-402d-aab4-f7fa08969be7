"""
Trades Service for QuantEdgeFlow
Business logic for trade execution, management, and analysis
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid

from sqlalchemy import select, update, delete, and_, or_, desc, func
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession

from config.database import get_async_session
from core.models.trades import (
    Trade, TradeLeg, TradeStatus, TradeType, OrderType,
    TradeCreate, TradeResponse, MultiLegTradeCreate, TradeLegResponse
)
from core.models.portfolio import Portfolio, PortfolioPosition
from services.portfolio_service import PortfolioService
from utils.logger import get_trading_logger, log_audit_event


logger = logging.getLogger(__name__)
trading_logger = get_trading_logger()


class TradesService:
    """Trade execution and management service."""
    
    def __init__(self):
        self.logger = logger
        self.portfolio_service = PortfolioService()
    
    async def create_trade(
        self, 
        user_id: str, 
        portfolio_id: uuid.UUID, 
        trade_data: TradeCreate
    ) -> Optional[TradeResponse]:
        """Create a new trade order."""
        async with get_async_session() as session:
            try:
                # Verify portfolio ownership
                portfolio_stmt = select(Portfolio).where(
                    and_(
                        Portfolio.id == portfolio_id,
                        Portfolio.user_id == user_id,
                        Portfolio.is_active == True
                    )
                )
                
                portfolio_result = await session.execute(portfolio_stmt)
                portfolio = portfolio_result.scalar_one_or_none()
                
                if not portfolio:
                    return None
                
                # Create trade instance
                trade = Trade(
                    portfolio_id=portfolio_id,
                    user_id=user_id,
                    symbol=trade_data.symbol.upper(),
                    instrument_type=trade_data.instrument_type,
                    trade_type=trade_data.trade_type,
                    order_type=trade_data.order_type,
                    time_in_force=trade_data.time_in_force,
                    quantity=trade_data.quantity,
                    price=trade_data.price,
                    stop_price=trade_data.stop_price,
                    option_type=trade_data.option_type,
                    strike_price=trade_data.strike_price,
                    expiration_date=trade_data.expiration_date,
                    trade_strategy=trade_data.trade_strategy,
                    max_loss=trade_data.max_loss,
                    target_profit=trade_data.target_profit,
                    notes=trade_data.notes,
                    tags=trade_data.tags,
                    status=TradeStatus.PENDING,
                    submitted_at=datetime.utcnow()
                )
                
                # Validate trade against portfolio constraints
                validation_result = await self._validate_trade(portfolio, trade)
                if not validation_result["valid"]:
                    raise ValueError(validation_result["reason"])
                
                session.add(trade)
                await session.commit()
                await session.refresh(trade)
                
                # Log trade creation
                log_audit_event(
                    "trade_created",
                    user_id,
                    {
                        "trade_id": str(trade.id),
                        "symbol": trade.symbol,
                        "trade_type": trade.trade_type,
                        "quantity": trade.quantity,
                        "order_type": trade.order_type
                    }
                )
                
                trading_logger.info(f"Trade created", extra={
                    "user_id": user_id,
                    "trade_id": str(trade.id),
                    "symbol": trade.symbol,
                    "trade_type": trade.trade_type,
                    "quantity": trade.quantity
                })
                
                # Simulate immediate execution for paper trading
                if portfolio.is_paper_trading:
                    await self._simulate_execution(session, trade)
                
                return TradeResponse.from_orm(trade)
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to create trade: {e}", extra={
                    "user_id": user_id,
                    "symbol": trade_data.symbol
                })
                raise
    
    async def create_multi_leg_trade(
        self, 
        user_id: str, 
        portfolio_id: uuid.UUID, 
        trade_data: MultiLegTradeCreate
    ) -> Optional[TradeResponse]:
        """Create a multi-leg options trade."""
        async with get_async_session() as session:
            try:
                # Verify portfolio ownership
                portfolio_stmt = select(Portfolio).where(
                    and_(
                        Portfolio.id == portfolio_id,
                        Portfolio.user_id == user_id,
                        Portfolio.is_active == True
                    )
                )
                
                portfolio_result = await session.execute(portfolio_stmt)
                portfolio = portfolio_result.scalar_one_or_none()
                
                if not portfolio:
                    return None
                
                # Create main trade record
                trade = Trade(
                    portfolio_id=portfolio_id,
                    user_id=user_id,
                    strategy_name=trade_data.strategy_name,
                    trade_strategy=trade_data.trade_strategy,
                    symbol=trade_data.legs[0].symbol.upper(),  # Use first leg's symbol
                    instrument_type="option",
                    trade_type="multi_leg",
                    order_type=trade_data.order_type,
                    time_in_force=trade_data.time_in_force,
                    quantity=1,  # Multi-leg trades are typically 1 unit
                    max_loss=trade_data.max_loss,
                    target_profit=trade_data.target_profit,
                    notes=trade_data.notes,
                    tags=trade_data.tags,
                    status=TradeStatus.PENDING,
                    submitted_at=datetime.utcnow()
                )
                
                session.add(trade)
                await session.flush()  # Get trade ID
                
                # Create trade legs
                for leg_data in trade_data.legs:
                    leg = TradeLeg(
                        trade_id=trade.id,
                        leg_number=leg_data.leg_number,
                        symbol=leg_data.symbol.upper(),
                        instrument_type=leg_data.instrument_type,
                        trade_type=leg_data.trade_type,
                        quantity=leg_data.quantity,
                        price=leg_data.price,
                        option_type=leg_data.option_type,
                        strike_price=leg_data.strike_price,
                        expiration_date=leg_data.expiration_date,
                        status=TradeStatus.PENDING
                    )
                    session.add(leg)
                
                await session.commit()
                await session.refresh(trade)
                
                # Log multi-leg trade creation
                log_audit_event(
                    "multi_leg_trade_created",
                    user_id,
                    {
                        "trade_id": str(trade.id),
                        "strategy_name": trade.strategy_name,
                        "trade_strategy": trade.trade_strategy,
                        "legs_count": len(trade_data.legs)
                    }
                )
                
                trading_logger.info(f"Multi-leg trade created", extra={
                    "user_id": user_id,
                    "trade_id": str(trade.id),
                    "strategy_name": trade.strategy_name,
                    "legs_count": len(trade_data.legs)
                })
                
                return TradeResponse.from_orm(trade)
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to create multi-leg trade: {e}", extra={
                    "user_id": user_id,
                    "strategy_name": trade_data.strategy_name
                })
                raise
    
    async def get_trades(
        self,
        user_id: str,
        portfolio_id: Optional[uuid.UUID] = None,
        symbol: Optional[str] = None,
        status: Optional[str] = None,
        trade_type: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[TradeResponse]:
        """Get trades with filtering options."""
        async with get_async_session() as session:
            try:
                # Build query conditions
                conditions = [Trade.user_id == user_id]
                
                if portfolio_id:
                    conditions.append(Trade.portfolio_id == portfolio_id)
                
                if symbol:
                    conditions.append(Trade.symbol == symbol.upper())
                
                if status:
                    conditions.append(Trade.status == status)
                
                if trade_type:
                    conditions.append(Trade.trade_type == trade_type)
                
                if start_date:
                    conditions.append(Trade.created_at >= datetime.combine(start_date, datetime.min.time()))
                
                if end_date:
                    conditions.append(Trade.created_at <= datetime.combine(end_date, datetime.max.time()))
                
                # Execute query
                stmt = select(Trade).where(
                    and_(*conditions)
                ).order_by(
                    desc(Trade.created_at)
                ).limit(limit).offset(offset)
                
                result = await session.execute(stmt)
                trades = result.scalars().all()
                
                return [TradeResponse.from_orm(trade) for trade in trades]
                
            except Exception as e:
                self.logger.error(f"Failed to get trades: {e}", extra={
                    "user_id": user_id
                })
                raise
    
    async def get_trade_by_id(
        self, 
        trade_id: uuid.UUID, 
        user_id: str
    ) -> Optional[TradeResponse]:
        """Get trade by ID."""
        async with get_async_session() as session:
            try:
                stmt = select(Trade).where(
                    and_(
                        Trade.id == trade_id,
                        Trade.user_id == user_id
                    )
                ).options(selectinload(Trade.legs))
                
                result = await session.execute(stmt)
                trade = result.scalar_one_or_none()
                
                if trade:
                    return TradeResponse.from_orm(trade)
                return None
                
            except Exception as e:
                self.logger.error(f"Failed to get trade: {e}", extra={
                    "user_id": user_id,
                    "trade_id": str(trade_id)
                })
                raise
    
    async def cancel_trade(
        self, 
        trade_id: uuid.UUID, 
        user_id: str
    ) -> Optional[TradeResponse]:
        """Cancel a pending trade."""
        async with get_async_session() as session:
            try:
                # Get trade
                stmt = select(Trade).where(
                    and_(
                        Trade.id == trade_id,
                        Trade.user_id == user_id,
                        Trade.status.in_([TradeStatus.PENDING, TradeStatus.PARTIALLY_FILLED])
                    )
                )
                
                result = await session.execute(stmt)
                trade = result.scalar_one_or_none()
                
                if not trade:
                    return None
                
                # Update trade status
                trade.status = TradeStatus.CANCELLED
                trade.cancelled_at = datetime.utcnow()
                trade.updated_at = datetime.utcnow()
                
                # Update legs if multi-leg trade
                if trade.legs:
                    for leg in trade.legs:
                        if leg.status in [TradeStatus.PENDING, TradeStatus.PARTIALLY_FILLED]:
                            leg.status = TradeStatus.CANCELLED
                
                await session.commit()
                
                # Log trade cancellation
                log_audit_event(
                    "trade_cancelled",
                    user_id,
                    {
                        "trade_id": str(trade.id),
                        "symbol": trade.symbol
                    }
                )
                
                trading_logger.info(f"Trade cancelled", extra={
                    "user_id": user_id,
                    "trade_id": str(trade.id)
                })
                
                return TradeResponse.from_orm(trade)
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to cancel trade: {e}", extra={
                    "user_id": user_id,
                    "trade_id": str(trade_id)
                })
                raise
    
    async def get_trade_legs(
        self, 
        trade_id: uuid.UUID, 
        user_id: str
    ) -> List[TradeLegResponse]:
        """Get legs for a multi-leg trade."""
        async with get_async_session() as session:
            try:
                # Verify trade ownership
                trade_stmt = select(Trade).where(
                    and_(
                        Trade.id == trade_id,
                        Trade.user_id == user_id
                    )
                )
                
                trade_result = await session.execute(trade_stmt)
                trade = trade_result.scalar_one_or_none()
                
                if not trade:
                    return []
                
                # Get trade legs
                legs_stmt = select(TradeLeg).where(
                    TradeLeg.trade_id == trade_id
                ).order_by(TradeLeg.leg_number)
                
                legs_result = await session.execute(legs_stmt)
                legs = legs_result.scalars().all()
                
                return [TradeLegResponse.from_orm(leg) for leg in legs]
                
            except Exception as e:
                self.logger.error(f"Failed to get trade legs: {e}", extra={
                    "user_id": user_id,
                    "trade_id": str(trade_id)
                })
                raise
    
    async def get_trading_summary(
        self,
        user_id: str,
        portfolio_id: Optional[uuid.UUID] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Get trading summary and statistics."""
        async with get_async_session() as session:
            try:
                # Build base conditions
                conditions = [Trade.user_id == user_id]
                
                if portfolio_id:
                    conditions.append(Trade.portfolio_id == portfolio_id)
                
                if start_date:
                    conditions.append(Trade.created_at >= datetime.combine(start_date, datetime.min.time()))
                
                if end_date:
                    conditions.append(Trade.created_at <= datetime.combine(end_date, datetime.max.time()))
                
                # Get trade statistics
                stats_stmt = select(
                    func.count(Trade.id).label('total_trades'),
                    func.count(Trade.id).filter(Trade.status == TradeStatus.FILLED).label('filled_trades'),
                    func.count(Trade.id).filter(Trade.status == TradeStatus.CANCELLED).label('cancelled_trades'),
                    func.sum(Trade.realized_pnl).label('total_pnl'),
                    func.avg(Trade.realized_pnl).label('avg_pnl')
                ).where(and_(*conditions))
                
                stats_result = await session.execute(stats_stmt)
                stats = stats_result.first()
                
                # Calculate additional metrics
                summary = {
                    "period": {
                        "start_date": start_date.isoformat() if start_date else None,
                        "end_date": end_date.isoformat() if end_date else None
                    },
                    "trade_counts": {
                        "total_trades": stats.total_trades or 0,
                        "filled_trades": stats.filled_trades or 0,
                        "cancelled_trades": stats.cancelled_trades or 0,
                        "pending_trades": (stats.total_trades or 0) - (stats.filled_trades or 0) - (stats.cancelled_trades or 0)
                    },
                    "financial_metrics": {
                        "total_pnl": float(stats.total_pnl or 0),
                        "average_pnl": float(stats.avg_pnl or 0),
                        "win_rate": 0.0,  # Would calculate from actual P&L data
                        "profit_factor": 0.0  # Would calculate from actual P&L data
                    },
                    "calculated_at": datetime.utcnow().isoformat()
                }
                
                return summary
                
            except Exception as e:
                self.logger.error(f"Failed to get trading summary: {e}", extra={
                    "user_id": user_id
                })
                raise
    
    async def get_trading_performance(
        self,
        user_id: str,
        portfolio_id: Optional[uuid.UUID] = None,
        symbol: Optional[str] = None,
        strategy: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Get detailed trading performance metrics."""
        # This would implement comprehensive performance analysis
        # For now, return a structured placeholder
        return {
            "performance_summary": {
                "total_return": 5250.75,
                "total_return_pct": 5.25,
                "win_rate": 68.5,
                "profit_factor": 1.85,
                "sharpe_ratio": 1.42,
                "max_drawdown": -2.8
            },
            "trade_analysis": {
                "total_trades": 45,
                "winning_trades": 31,
                "losing_trades": 14,
                "average_win": 285.50,
                "average_loss": -125.75,
                "largest_win": 1250.00,
                "largest_loss": -450.00
            },
            "strategy_breakdown": {
                "covered_call": {"trades": 15, "pnl": 2100.50, "win_rate": 73.3},
                "cash_secured_put": {"trades": 12, "pnl": 1850.25, "win_rate": 66.7},
                "iron_condor": {"trades": 8, "pnl": 950.00, "win_rate": 62.5}
            },
            "calculated_at": datetime.utcnow().isoformat()
        }
    
    async def simulate_trade(
        self,
        user_id: str,
        portfolio_id: uuid.UUID,
        trade_data: TradeCreate
    ) -> Dict[str, Any]:
        """Simulate a trade without executing it."""
        # This would implement trade simulation logic
        # For now, return a structured placeholder
        return {
            "simulation_results": {
                "estimated_cost": float(trade_data.price or 0) * abs(trade_data.quantity),
                "estimated_commission": 1.00,
                "estimated_total_cost": float(trade_data.price or 0) * abs(trade_data.quantity) + 1.00,
                "buying_power_required": float(trade_data.price or 0) * abs(trade_data.quantity) * 0.5,
                "margin_impact": 0.0,
                "portfolio_impact": {
                    "delta_change": 0.0,
                    "gamma_change": 0.0,
                    "theta_change": 0.0,
                    "vega_change": 0.0
                }
            },
            "risk_analysis": {
                "max_profit": None,
                "max_loss": None,
                "break_even_price": None,
                "probability_of_profit": None
            },
            "simulated_at": datetime.utcnow().isoformat()
        }
    
    async def _validate_trade(self, portfolio: Portfolio, trade: Trade) -> Dict[str, Any]:
        """Validate trade against portfolio constraints."""
        # Basic validation - would implement comprehensive checks
        if portfolio.cash_balance < 1000:  # Minimum cash requirement
            return {"valid": False, "reason": "Insufficient cash balance"}
        
        return {"valid": True, "reason": None}
    
    async def _simulate_execution(self, session: AsyncSession, trade: Trade) -> None:
        """Simulate immediate execution for paper trading."""
        # For paper trading, simulate immediate fill
        trade.status = TradeStatus.FILLED
        trade.filled_quantity = trade.quantity
        trade.filled_price = trade.price or Decimal("100.00")  # Mock price
        trade.filled_at = datetime.utcnow()
        trade.commission = Decimal("1.00")  # Mock commission
        trade.total_cost = trade.filled_price * abs(trade.filled_quantity) + trade.commission
