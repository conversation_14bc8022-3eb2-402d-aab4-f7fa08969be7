version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: quantedgeflow_postgres
    environment:
      POSTGRES_DB: quantedgeflow
      POSTGRES_USER: quantuser
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-quantpass123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    networks:
      - quantedgeflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quantuser -d quantedgeflow"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: quantedgeflow_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redispass123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - quantedgeflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # QuantEdgeFlow API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: quantedgeflow_api
    environment:
      - DATABASE_URL=postgresql+asyncpg://quantuser:${POSTGRES_PASSWORD:-quantpass123}@postgres:5432/quantedgeflow
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redispass123}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - DEBUG=${DEBUG:-false}
      - HOST=0.0.0.0
      - PORT=8000
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quantedgeflow_network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for Background Tasks
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: quantedgeflow_worker
    command: celery -A src.services.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://quantuser:${POSTGRES_PASSWORD:-quantpass123}@postgres:5432/quantedgeflow
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redispass123}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quantedgeflow_network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # Celery Beat Scheduler
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: quantedgeflow_scheduler
    command: celery -A src.services.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://quantuser:${POSTGRES_PASSWORD:-quantpass123}@postgres:5432/quantedgeflow
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redispass123}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quantedgeflow_network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: quantedgeflow_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - api
    networks:
      - quantedgeflow_network
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: quantedgeflow_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - quantedgeflow_network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: quantedgeflow_grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - quantedgeflow_network
    restart: unless-stopped

  # TimescaleDB for Time Series Data
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: quantedgeflow_timescaledb
    environment:
      POSTGRES_DB: timeseries
      POSTGRES_USER: timeuser
      POSTGRES_PASSWORD: ${TIMESCALE_PASSWORD:-timepass123}
    volumes:
      - timescale_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - quantedgeflow_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  timescale_data:
    driver: local

networks:
  quantedgeflow_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
