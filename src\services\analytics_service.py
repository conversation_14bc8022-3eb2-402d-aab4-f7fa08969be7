"""
Analytics Service for QuantEdgeFlow
Advanced analytics, risk metrics, and performance analysis
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid
import numpy as np
import pandas as pd
from scipy import stats

from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from config.database import get_async_session
from core.models.portfolio import Portfolio, PortfolioPosition
from core.models.trades import Trade, TradeStatus
from core.models.market_data import HistoricalPrice, StockQuote
from core.analytics.risk_metrics import RiskMetricsCalculator
from core.analytics.portfolio_optimizer import PortfolioOptimizer
from utils.logger import get_performance_logger


logger = logging.getLogger(__name__)
performance_logger = get_performance_logger()


class AnalyticsService:
    """Advanced analytics and risk management service."""
    
    def __init__(self):
        self.logger = logger
        self.risk_calculator = RiskMetricsCalculator()
        self.optimizer = PortfolioOptimizer()
    
    async def calculate_portfolio_performance(
        self, 
        portfolio_id: uuid.UUID, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Calculate comprehensive portfolio performance metrics."""
        async with get_async_session() as session:
            try:
                start_time = datetime.utcnow()
                
                if end_date is None:
                    end_date = date.today()
                if start_date is None:
                    start_date = end_date - timedelta(days=365)  # 1 year default
                
                # Get portfolio
                portfolio_stmt = select(Portfolio).where(Portfolio.id == portfolio_id)
                portfolio_result = await session.execute(portfolio_stmt)
                portfolio = portfolio_result.scalar_one_or_none()
                
                if not portfolio:
                    raise ValueError(f"Portfolio {portfolio_id} not found")
                
                # Get historical trades
                trades_stmt = select(Trade).where(
                    and_(
                        Trade.portfolio_id == portfolio_id,
                        Trade.status == TradeStatus.FILLED,
                        Trade.filled_at >= datetime.combine(start_date, datetime.min.time()),
                        Trade.filled_at <= datetime.combine(end_date, datetime.max.time())
                    )
                ).order_by(Trade.filled_at)
                
                trades_result = await session.execute(trades_stmt)
                trades = trades_result.scalars().all()
                
                # Calculate performance metrics
                performance_metrics = await self._calculate_performance_metrics(
                    portfolio, trades, start_date, end_date
                )
                
                # Calculate risk metrics
                risk_metrics = await self._calculate_risk_metrics(
                    portfolio, trades, start_date, end_date
                )
                
                # Calculate trading statistics
                trading_stats = await self._calculate_trading_statistics(trades)
                
                # Calculate sector allocation
                sector_allocation = await self._calculate_sector_allocation(portfolio_id)
                
                # Calculate correlation matrix
                correlation_matrix = await self._calculate_correlation_matrix(portfolio_id)
                
                # Log performance calculation
                duration = (datetime.utcnow() - start_time).total_seconds()
                performance_logger.info(f"Portfolio performance calculated", extra={
                    "portfolio_id": str(portfolio_id),
                    "trades_count": len(trades),
                    "duration_seconds": duration
                })
                
                return {
                    "portfolio_id": str(portfolio_id),
                    "analysis_period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "days": (end_date - start_date).days
                    },
                    "performance_metrics": performance_metrics,
                    "risk_metrics": risk_metrics,
                    "trading_statistics": trading_stats,
                    "sector_allocation": sector_allocation,
                    "correlation_matrix": correlation_matrix,
                    "calculated_at": datetime.utcnow().isoformat()
                }
                
            except Exception as e:
                self.logger.error(f"Failed to calculate portfolio performance: {e}", extra={
                    "portfolio_id": str(portfolio_id)
                })
                raise
    
    async def calculate_var_and_cvar(
        self, 
        portfolio_id: uuid.UUID, 
        confidence_level: float = 0.95,
        time_horizon: int = 1
    ) -> Dict[str, float]:
        """Calculate Value at Risk (VaR) and Conditional VaR."""
        async with get_async_session() as session:
            try:
                # Get portfolio positions
                positions_stmt = select(PortfolioPosition).where(
                    and_(
                        PortfolioPosition.portfolio_id == portfolio_id,
                        PortfolioPosition.status == "open"
                    )
                )
                
                positions_result = await session.execute(positions_stmt)
                positions = positions_result.scalars().all()
                
                if not positions:
                    return {"var": 0.0, "cvar": 0.0, "portfolio_value": 0.0}
                
                # Get historical returns for each position
                returns_data = {}
                portfolio_value = 0.0
                
                for position in positions:
                    # Get historical prices
                    prices_stmt = select(HistoricalPrice).where(
                        and_(
                            HistoricalPrice.symbol == position.symbol,
                            HistoricalPrice.date >= date.today() - timedelta(days=252)  # 1 year
                        )
                    ).order_by(HistoricalPrice.date)
                    
                    prices_result = await session.execute(prices_stmt)
                    prices = prices_result.scalars().all()
                    
                    if len(prices) > 1:
                        # Calculate daily returns
                        price_series = [float(p.close_price) for p in prices]
                        returns = np.diff(np.log(price_series))
                        returns_data[position.symbol] = {
                            "returns": returns,
                            "position_value": float(position.market_value or 0),
                            "weight": 0.0  # Will calculate after getting total value
                        }
                        portfolio_value += float(position.market_value or 0)
                
                # Calculate position weights
                for symbol in returns_data:
                    returns_data[symbol]["weight"] = returns_data[symbol]["position_value"] / portfolio_value
                
                # Calculate portfolio returns
                min_length = min(len(data["returns"]) for data in returns_data.values())
                portfolio_returns = np.zeros(min_length)
                
                for symbol, data in returns_data.items():
                    portfolio_returns += data["weight"] * data["returns"][:min_length]
                
                # Scale returns for time horizon
                scaled_returns = portfolio_returns * np.sqrt(time_horizon)
                
                # Calculate VaR
                var_percentile = (1 - confidence_level) * 100
                var = np.percentile(scaled_returns, var_percentile) * portfolio_value
                
                # Calculate CVaR (Expected Shortfall)
                var_threshold = np.percentile(scaled_returns, var_percentile)
                tail_returns = scaled_returns[scaled_returns <= var_threshold]
                cvar = np.mean(tail_returns) * portfolio_value if len(tail_returns) > 0 else var
                
                return {
                    "var": abs(var),  # Return as positive value
                    "cvar": abs(cvar),  # Return as positive value
                    "portfolio_value": portfolio_value,
                    "confidence_level": confidence_level,
                    "time_horizon_days": time_horizon,
                    "observations": len(portfolio_returns)
                }
                
            except Exception as e:
                self.logger.error(f"Failed to calculate VaR/CVaR: {e}", extra={
                    "portfolio_id": str(portfolio_id)
                })
                raise
    
    async def generate_trade_signals(
        self, 
        symbols: List[str],
        strategy_type: str = "momentum"
    ) -> List[Dict[str, Any]]:
        """Generate trading signals based on technical analysis."""
        async with get_async_session() as session:
            try:
                signals = []
                
                for symbol in symbols:
                    # Get recent historical data
                    prices_stmt = select(HistoricalPrice).where(
                        and_(
                            HistoricalPrice.symbol == symbol.upper(),
                            HistoricalPrice.date >= date.today() - timedelta(days=100)
                        )
                    ).order_by(HistoricalPrice.date)
                    
                    prices_result = await session.execute(prices_stmt)
                    prices = prices_result.scalars().all()
                    
                    if len(prices) < 50:  # Need at least 50 days of data
                        continue
                    
                    # Convert to pandas DataFrame for analysis
                    df = pd.DataFrame([{
                        "date": p.date,
                        "open": float(p.open_price),
                        "high": float(p.high_price),
                        "low": float(p.low_price),
                        "close": float(p.close_price),
                        "volume": p.volume
                    } for p in prices])
                    
                    # Generate signals based on strategy
                    if strategy_type == "momentum":
                        signal = self._generate_momentum_signal(df)
                    elif strategy_type == "mean_reversion":
                        signal = self._generate_mean_reversion_signal(df)
                    elif strategy_type == "breakout":
                        signal = self._generate_breakout_signal(df)
                    else:
                        signal = None
                    
                    if signal:
                        signal["symbol"] = symbol.upper()
                        signal["generated_at"] = datetime.utcnow().isoformat()
                        signals.append(signal)
                
                return signals
                
            except Exception as e:
                self.logger.error(f"Failed to generate trade signals: {e}", extra={
                    "symbols": symbols,
                    "strategy_type": strategy_type
                })
                raise
    
    async def _calculate_performance_metrics(
        self, 
        portfolio: Portfolio, 
        trades: List[Trade], 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """Calculate performance metrics."""
        if not trades:
            return {
                "total_return": 0.0,
                "total_return_pct": 0.0,
                "annualized_return": 0.0,
                "volatility": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "calmar_ratio": 0.0
            }
        
        # Calculate daily returns
        daily_pnl = {}
        for trade in trades:
            trade_date = trade.filled_at.date()
            if trade_date not in daily_pnl:
                daily_pnl[trade_date] = 0.0
            daily_pnl[trade_date] += float(trade.realized_pnl or 0)
        
        # Convert to returns series
        returns = list(daily_pnl.values())
        
        if not returns:
            return {"total_return": 0.0, "total_return_pct": 0.0}
        
        total_return = sum(returns)
        initial_capital = float(portfolio.initial_capital)
        total_return_pct = (total_return / initial_capital) * 100
        
        # Calculate annualized return
        days = (end_date - start_date).days
        years = days / 365.25
        annualized_return = ((1 + total_return / initial_capital) ** (1 / years) - 1) * 100 if years > 0 else 0
        
        # Calculate volatility
        if len(returns) > 1:
            volatility = np.std(returns) * np.sqrt(252)  # Annualized
        else:
            volatility = 0.0
        
        # Calculate Sharpe ratio (assuming 5% risk-free rate)
        risk_free_rate = 0.05
        sharpe_ratio = (annualized_return / 100 - risk_free_rate) / (volatility / 100) if volatility > 0 else 0
        
        # Calculate maximum drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / initial_capital * 100
        max_drawdown = np.min(drawdown) if len(drawdown) > 0 else 0
        
        # Calculate Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        return {
            "total_return": total_return,
            "total_return_pct": total_return_pct,
            "annualized_return": annualized_return,
            "volatility": volatility,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown,
            "calmar_ratio": calmar_ratio
        }
    
    async def _calculate_risk_metrics(
        self, 
        portfolio: Portfolio, 
        trades: List[Trade], 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """Calculate risk metrics."""
        # This would implement comprehensive risk calculations
        # For now, return basic metrics
        return {
            "portfolio_beta": 1.0,
            "tracking_error": 0.0,
            "information_ratio": 0.0,
            "sortino_ratio": 0.0,
            "downside_deviation": 0.0
        }
    
    async def _calculate_trading_statistics(self, trades: List[Trade]) -> Dict[str, Any]:
        """Calculate trading statistics."""
        if not trades:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "average_win": 0.0,
                "average_loss": 0.0,
                "largest_win": 0.0,
                "largest_loss": 0.0
            }
        
        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if (t.realized_pnl or 0) > 0)
        losing_trades = sum(1 for t in trades if (t.realized_pnl or 0) < 0)
        
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        wins = [float(t.realized_pnl) for t in trades if (t.realized_pnl or 0) > 0]
        losses = [float(t.realized_pnl) for t in trades if (t.realized_pnl or 0) < 0]
        
        total_wins = sum(wins) if wins else 0
        total_losses = abs(sum(losses)) if losses else 0
        
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        average_win = total_wins / len(wins) if wins else 0
        average_loss = total_losses / len(losses) if losses else 0
        largest_win = max(wins) if wins else 0
        largest_loss = min(losses) if losses else 0
        
        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "average_win": average_win,
            "average_loss": average_loss,
            "largest_win": largest_win,
            "largest_loss": largest_loss
        }
    
    async def _calculate_sector_allocation(self, portfolio_id: uuid.UUID) -> Dict[str, float]:
        """Calculate sector allocation."""
        # This would implement sector allocation calculation
        # For now, return placeholder
        return {"Technology": 40.0, "Healthcare": 30.0, "Finance": 20.0, "Other": 10.0}
    
    async def _calculate_correlation_matrix(self, portfolio_id: uuid.UUID) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix between positions."""
        # This would implement correlation calculation
        # For now, return placeholder
        return {}
    
    def _generate_momentum_signal(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Generate momentum-based trading signal."""
        if len(df) < 20:
            return None
        
        # Calculate moving averages
        df["sma_10"] = df["close"].rolling(10).mean()
        df["sma_20"] = df["close"].rolling(20).mean()
        
        current_price = df["close"].iloc[-1]
        sma_10 = df["sma_10"].iloc[-1]
        sma_20 = df["sma_20"].iloc[-1]
        
        # Generate signal
        if sma_10 > sma_20 and current_price > sma_10:
            signal_type = "BUY"
            strength = min((sma_10 - sma_20) / sma_20 * 100, 100)
        elif sma_10 < sma_20 and current_price < sma_10:
            signal_type = "SELL"
            strength = min((sma_20 - sma_10) / sma_20 * 100, 100)
        else:
            signal_type = "HOLD"
            strength = 0
        
        return {
            "signal_type": signal_type,
            "strength": strength,
            "current_price": current_price,
            "sma_10": sma_10,
            "sma_20": sma_20,
            "strategy": "momentum"
        }
    
    def _generate_mean_reversion_signal(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Generate mean reversion signal."""
        # Placeholder implementation
        return None
    
    def _generate_breakout_signal(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Generate breakout signal."""
        # Placeholder implementation
        return None
