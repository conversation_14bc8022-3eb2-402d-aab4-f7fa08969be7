"""
File Storage Manager for QuantEdgeFlow
Local and cloud file storage for data persistence
"""

import logging
import os
import json
import pickle
import gzip
from typing import Any, Optional, Dict, List, Union
from datetime import datetime
from pathlib import Path
import pandas as pd
import aiofiles
import aiofiles.os

from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class FileStorageManager:
    """Advanced file storage manager for local and cloud storage."""
    
    def __init__(self):
        self.logger = logger
        self.base_path = Path("data")
        self.ensure_directories()
        
        # Storage paths
        self.paths = {
            'market_data': self.base_path / "market_data",
            'options_data': self.base_path / "options_data", 
            'fundamental_data': self.base_path / "fundamental_data",
            'analytics': self.base_path / "analytics",
            'backups': self.base_path / "backups",
            'exports': self.base_path / "exports",
            'logs': self.base_path / "logs"
        }
    
    def ensure_directories(self):
        """Ensure all required directories exist."""
        try:
            self.base_path.mkdir(exist_ok=True)
            
            for path_name in ['market_data', 'options_data', 'fundamental_data', 
                             'analytics', 'backups', 'exports', 'logs']:
                (self.base_path / path_name).mkdir(exist_ok=True)
                
        except Exception as e:
            self.logger.error(f"Failed to create directories: {e}")
    
    async def save_dataframe(
        self, 
        df: pd.DataFrame, 
        filename: str, 
        storage_type: str = 'market_data',
        format: str = 'parquet',
        compress: bool = True
    ) -> bool:
        """Save DataFrame to file."""
        try:
            if df.empty:
                self.logger.warning(f"Attempting to save empty DataFrame: {filename}")
                return False
            
            file_path = self.paths[storage_type] / filename
            
            if format == 'parquet':
                if compress:
                    df.to_parquet(file_path.with_suffix('.parquet.gz'), compression='gzip')
                else:
                    df.to_parquet(file_path.with_suffix('.parquet'))
            elif format == 'csv':
                if compress:
                    df.to_csv(file_path.with_suffix('.csv.gz'), compression='gzip')
                else:
                    df.to_csv(file_path.with_suffix('.csv'))
            elif format == 'pickle':
                if compress:
                    with gzip.open(file_path.with_suffix('.pkl.gz'), 'wb') as f:
                        pickle.dump(df, f)
                else:
                    df.to_pickle(file_path.with_suffix('.pkl'))
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            self.logger.info(f"Saved DataFrame to {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save DataFrame {filename}: {e}")
            return False
    
    async def load_dataframe(
        self, 
        filename: str, 
        storage_type: str = 'market_data',
        format: str = 'parquet'
    ) -> Optional[pd.DataFrame]:
        """Load DataFrame from file."""
        try:
            file_path = self.paths[storage_type] / filename
            
            # Try different extensions if file doesn't exist
            possible_extensions = ['.parquet.gz', '.parquet', '.csv.gz', '.csv', '.pkl.gz', '.pkl']
            
            actual_file = None
            for ext in possible_extensions:
                test_path = file_path.with_suffix(ext)
                if test_path.exists():
                    actual_file = test_path
                    break
            
            if not actual_file:
                self.logger.warning(f"File not found: {filename}")
                return None
            
            # Load based on extension
            if actual_file.suffix == '.parquet' or actual_file.suffixes == ['.parquet', '.gz']:
                df = pd.read_parquet(actual_file)
            elif actual_file.suffix == '.csv' or actual_file.suffixes == ['.csv', '.gz']:
                df = pd.read_csv(actual_file, index_col=0)
            elif actual_file.suffix == '.pkl':
                df = pd.read_pickle(actual_file)
            elif actual_file.suffixes == ['.pkl', '.gz']:
                with gzip.open(actual_file, 'rb') as f:
                    df = pickle.load(f)
            else:
                raise ValueError(f"Unsupported file format: {actual_file}")
            
            self.logger.info(f"Loaded DataFrame from {actual_file}")
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to load DataFrame {filename}: {e}")
            return None
    
    async def save_json(
        self, 
        data: Dict[str, Any], 
        filename: str, 
        storage_type: str = 'analytics',
        compress: bool = False
    ) -> bool:
        """Save JSON data to file."""
        try:
            file_path = self.paths[storage_type] / filename
            
            if compress:
                file_path = file_path.with_suffix('.json.gz')
                async with aiofiles.open(file_path, 'wb') as f:
                    json_str = json.dumps(data, default=str, indent=2)
                    compressed_data = gzip.compress(json_str.encode())
                    await f.write(compressed_data)
            else:
                file_path = file_path.with_suffix('.json')
                async with aiofiles.open(file_path, 'w') as f:
                    await f.write(json.dumps(data, default=str, indent=2))
            
            self.logger.info(f"Saved JSON to {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save JSON {filename}: {e}")
            return False
    
    async def load_json(
        self, 
        filename: str, 
        storage_type: str = 'analytics'
    ) -> Optional[Dict[str, Any]]:
        """Load JSON data from file."""
        try:
            file_path = self.paths[storage_type] / filename
            
            # Try both compressed and uncompressed
            for ext in ['.json.gz', '.json']:
                test_path = file_path.with_suffix(ext)
                if test_path.exists():
                    if ext == '.json.gz':
                        async with aiofiles.open(test_path, 'rb') as f:
                            compressed_data = await f.read()
                            json_str = gzip.decompress(compressed_data).decode()
                            return json.loads(json_str)
                    else:
                        async with aiofiles.open(test_path, 'r') as f:
                            content = await f.read()
                            return json.loads(content)
            
            self.logger.warning(f"JSON file not found: {filename}")
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to load JSON {filename}: {e}")
            return None
    
    async def save_market_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """Save market data for a symbol."""
        filename = f"{symbol}_{datetime.now().strftime('%Y%m%d')}"
        return await self.save_dataframe(data, filename, 'market_data')
    
    async def load_market_data(self, symbol: str, date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Load market data for a symbol."""
        if date:
            filename = f"{symbol}_{date}"
        else:
            # Load most recent file
            files = list(self.paths['market_data'].glob(f"{symbol}_*"))
            if not files:
                return None
            filename = max(files).stem
        
        return await self.load_dataframe(filename, 'market_data')
    
    async def save_options_data(self, symbol: str, expiry: str, data: pd.DataFrame) -> bool:
        """Save options data for a symbol and expiry."""
        filename = f"{symbol}_{expiry}_{datetime.now().strftime('%Y%m%d_%H%M')}"
        return await self.save_dataframe(data, filename, 'options_data')
    
    async def load_options_data(self, symbol: str, expiry: str) -> Optional[pd.DataFrame]:
        """Load options data for a symbol and expiry."""
        # Find most recent file for this symbol and expiry
        pattern = f"{symbol}_{expiry}_*"
        files = list(self.paths['options_data'].glob(pattern))
        
        if not files:
            return None
        
        # Get most recent file
        latest_file = max(files, key=lambda x: x.stat().st_mtime)
        return await self.load_dataframe(latest_file.stem, 'options_data')
    
    async def backup_data(self, backup_name: str) -> bool:
        """Create backup of all data."""
        try:
            backup_path = self.paths['backups'] / f"{backup_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_path.mkdir(exist_ok=True)
            
            # Backup each data type
            for data_type, source_path in self.paths.items():
                if data_type in ['backups', 'logs']:
                    continue
                
                dest_path = backup_path / data_type
                dest_path.mkdir(exist_ok=True)
                
                # Copy files
                for file_path in source_path.glob('*'):
                    if file_path.is_file():
                        dest_file = dest_path / file_path.name
                        async with aiofiles.open(file_path, 'rb') as src:
                            async with aiofiles.open(dest_file, 'wb') as dst:
                                content = await src.read()
                                await dst.write(content)
            
            self.logger.info(f"Created backup: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create backup: {e}")
            return False
    
    async def cleanup_old_files(self, storage_type: str, days_to_keep: int) -> int:
        """Clean up old files."""
        try:
            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
            deleted_count = 0
            
            for file_path in self.paths[storage_type].glob('*'):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    await aiofiles.os.remove(file_path)
                    deleted_count += 1
            
            self.logger.info(f"Cleaned up {deleted_count} old files from {storage_type}")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old files: {e}")
            return 0
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        try:
            stats = {}
            
            for storage_type, path in self.paths.items():
                file_count = len(list(path.glob('*')))
                total_size = sum(f.stat().st_size for f in path.glob('*') if f.is_file())
                
                stats[storage_type] = {
                    'file_count': file_count,
                    'total_size_bytes': total_size,
                    'total_size_mb': round(total_size / (1024 * 1024), 2)
                }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get storage stats: {e}")
            return {}
    
    async def export_data(
        self, 
        data: Union[pd.DataFrame, Dict[str, Any]], 
        filename: str, 
        format: str = 'csv'
    ) -> Optional[str]:
        """Export data for external use."""
        try:
            export_path = self.paths['exports'] / filename
            
            if isinstance(data, pd.DataFrame):
                if format == 'csv':
                    data.to_csv(export_path.with_suffix('.csv'))
                elif format == 'excel':
                    data.to_excel(export_path.with_suffix('.xlsx'))
                elif format == 'json':
                    data.to_json(export_path.with_suffix('.json'), orient='records', indent=2)
                else:
                    raise ValueError(f"Unsupported format for DataFrame: {format}")
            elif isinstance(data, dict):
                if format == 'json':
                    async with aiofiles.open(export_path.with_suffix('.json'), 'w') as f:
                        await f.write(json.dumps(data, default=str, indent=2))
                else:
                    raise ValueError(f"Unsupported format for dict: {format}")
            else:
                raise ValueError(f"Unsupported data type: {type(data)}")
            
            final_path = str(export_path.with_suffix(f'.{format}'))
            self.logger.info(f"Exported data to {final_path}")
            return final_path
            
        except Exception as e:
            self.logger.error(f"Failed to export data: {e}")
            return None


# Global file storage manager instance
file_storage_manager = FileStorageManager()
