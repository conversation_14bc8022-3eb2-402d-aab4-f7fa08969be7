"""
API Middleware Package for QuantEdgeFlow
Authentication, rate limiting, and request processing middleware
"""

from .auth import (
    AuthManager,
    AuthMiddleware,
    get_current_user,
    get_current_user_with_permissions,
    require_permission,
    require_role,
    Permission,
    UserRole,
    auth_manager
)

from .rate_limiting import (
    RateLimitMiddleware,
    RedisRateLimiter,
    RateLimitTier,
    RATE_LIMIT_TIERS,
    rate_limiter,
    init_rate_limiter,
    close_rate_limiter
)

__all__ = [
    # Authentication
    "AuthManager",
    "AuthMiddleware", 
    "get_current_user",
    "get_current_user_with_permissions",
    "require_permission",
    "require_role",
    "Permission",
    "UserRole",
    "auth_manager",
    
    # Rate Limiting
    "RateLimitMiddleware",
    "RedisRateLimiter",
    "RateLimitTier", 
    "RATE_LIMIT_TIERS",
    "rate_limiter",
    "init_rate_limiter",
    "close_rate_limiter"
]
