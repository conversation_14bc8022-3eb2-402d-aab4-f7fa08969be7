"""
Technical Indicators for QuantEdgeFlow
Comprehensive technical analysis indicators for market analysis
"""

import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple, Union
import warnings

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """Comprehensive technical indicators calculator."""
    
    def __init__(self):
        self.logger = logger
    
    def sma(self, data: Union[pd.Series, np.ndarray], period: int) -> np.ndarray:
        """Simple Moving Average."""
        try:
            if isinstance(data, pd.Series):
                return data.rolling(window=period).mean().values
            else:
                return pd.Series(data).rolling(window=period).mean().values
        except Exception as e:
            self.logger.error(f"Failed to calculate SMA: {e}")
            return np.full(len(data), np.nan)
    
    def ema(self, data: Union[pd.Series, np.ndarray], period: int) -> np.ndarray:
        """Exponential Moving Average."""
        try:
            if isinstance(data, pd.Series):
                return data.ewm(span=period).mean().values
            else:
                return pd.Series(data).ewm(span=period).mean().values
        except Exception as e:
            self.logger.error(f"Failed to calculate EMA: {e}")
            return np.full(len(data), np.nan)
    
    def rsi(self, data: Union[pd.Series, np.ndarray], period: int = 14) -> np.ndarray:
        """Relative Strength Index."""
        try:
            if isinstance(data, np.ndarray):
                data = pd.Series(data)
            
            delta = data.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.values
        except Exception as e:
            self.logger.error(f"Failed to calculate RSI: {e}")
            return np.full(len(data), np.nan)
    
    def macd(
        self, 
        data: Union[pd.Series, np.ndarray], 
        fast_period: int = 12, 
        slow_period: int = 26, 
        signal_period: int = 9
    ) -> Dict[str, np.ndarray]:
        """MACD (Moving Average Convergence Divergence)."""
        try:
            if isinstance(data, np.ndarray):
                data = pd.Series(data)
            
            ema_fast = data.ewm(span=fast_period).mean()
            ema_slow = data.ewm(span=slow_period).mean()
            
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal_period).mean()
            histogram = macd_line - signal_line
            
            return {
                'macd': macd_line.values,
                'signal': signal_line.values,
                'histogram': histogram.values
            }
        except Exception as e:
            self.logger.error(f"Failed to calculate MACD: {e}")
            return {
                'macd': np.full(len(data), np.nan),
                'signal': np.full(len(data), np.nan),
                'histogram': np.full(len(data), np.nan)
            }
    
    def bollinger_bands(
        self, 
        data: Union[pd.Series, np.ndarray], 
        period: int = 20, 
        std_dev: float = 2.0
    ) -> Dict[str, np.ndarray]:
        """Bollinger Bands."""
        try:
            if isinstance(data, np.ndarray):
                data = pd.Series(data)
            
            sma = data.rolling(window=period).mean()
            std = data.rolling(window=period).std()
            
            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)
            
            return {
                'upper': upper_band.values,
                'middle': sma.values,
                'lower': lower_band.values,
                'bandwidth': ((upper_band - lower_band) / sma).values,
                'percent_b': ((data - lower_band) / (upper_band - lower_band)).values
            }
        except Exception as e:
            self.logger.error(f"Failed to calculate Bollinger Bands: {e}")
            return {
                'upper': np.full(len(data), np.nan),
                'middle': np.full(len(data), np.nan),
                'lower': np.full(len(data), np.nan),
                'bandwidth': np.full(len(data), np.nan),
                'percent_b': np.full(len(data), np.nan)
            }
    
    def stochastic(
        self, 
        high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        k_period: int = 14, 
        d_period: int = 3
    ) -> Dict[str, np.ndarray]:
        """Stochastic Oscillator."""
        try:
            if isinstance(high, np.ndarray):
                high = pd.Series(high)
                low = pd.Series(low)
                close = pd.Series(close)
            
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_period).mean()
            
            return {
                'k_percent': k_percent.values,
                'd_percent': d_percent.values
            }
        except Exception as e:
            self.logger.error(f"Failed to calculate Stochastic: {e}")
            return {
                'k_percent': np.full(len(close), np.nan),
                'd_percent': np.full(len(close), np.nan)
            }
    
    def atr(
        self, 
        high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 14
    ) -> np.ndarray:
        """Average True Range."""
        try:
            if isinstance(high, np.ndarray):
                high = pd.Series(high)
                low = pd.Series(low)
                close = pd.Series(close)
            
            prev_close = close.shift(1)
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean()
            
            return atr.values
        except Exception as e:
            self.logger.error(f"Failed to calculate ATR: {e}")
            return np.full(len(close), np.nan)
    
    def adx(
        self, 
        high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 14
    ) -> Dict[str, np.ndarray]:
        """Average Directional Index."""
        try:
            if isinstance(high, np.ndarray):
                high = pd.Series(high)
                low = pd.Series(low)
                close = pd.Series(close)
            
            # Calculate True Range
            prev_close = close.shift(1)
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # Calculate Directional Movement
            up_move = high - high.shift(1)
            down_move = low.shift(1) - low
            
            plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
            minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
            
            # Smooth the values
            atr = true_range.rolling(window=period).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=period).mean() / atr)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=period).mean() / atr)
            
            # Calculate ADX
            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            adx = dx.rolling(window=period).mean()
            
            return {
                'adx': adx.values,
                'plus_di': plus_di.values,
                'minus_di': minus_di.values
            }
        except Exception as e:
            self.logger.error(f"Failed to calculate ADX: {e}")
            return {
                'adx': np.full(len(close), np.nan),
                'plus_di': np.full(len(close), np.nan),
                'minus_di': np.full(len(close), np.nan)
            }
    
    def cci(
        self, 
        high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 20
    ) -> np.ndarray:
        """Commodity Channel Index."""
        try:
            if isinstance(high, np.ndarray):
                high = pd.Series(high)
                low = pd.Series(low)
                close = pd.Series(close)
            
            typical_price = (high + low + close) / 3
            sma_tp = typical_price.rolling(window=period).mean()
            mean_deviation = typical_price.rolling(window=period).apply(
                lambda x: np.mean(np.abs(x - np.mean(x)))
            )
            
            cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
            
            return cci.values
        except Exception as e:
            self.logger.error(f"Failed to calculate CCI: {e}")
            return np.full(len(close), np.nan)
    
    def williams_r(
        self, 
        high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 14
    ) -> np.ndarray:
        """Williams %R."""
        try:
            if isinstance(high, np.ndarray):
                high = pd.Series(high)
                low = pd.Series(low)
                close = pd.Series(close)
            
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            
            williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
            
            return williams_r.values
        except Exception as e:
            self.logger.error(f"Failed to calculate Williams %R: {e}")
            return np.full(len(close), np.nan)
    
    def obv(
        self, 
        close: Union[pd.Series, np.ndarray], 
        volume: Union[pd.Series, np.ndarray]
    ) -> np.ndarray:
        """On-Balance Volume."""
        try:
            if isinstance(close, np.ndarray):
                close = pd.Series(close)
                volume = pd.Series(volume)
            
            price_change = close.diff()
            obv = np.where(price_change > 0, volume, 
                          np.where(price_change < 0, -volume, 0))
            
            return pd.Series(obv).cumsum().values
        except Exception as e:
            self.logger.error(f"Failed to calculate OBV: {e}")
            return np.full(len(close), np.nan)
    
    def vwap(
        self, 
        high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        volume: Union[pd.Series, np.ndarray]
    ) -> np.ndarray:
        """Volume Weighted Average Price."""
        try:
            if isinstance(high, np.ndarray):
                high = pd.Series(high)
                low = pd.Series(low)
                close = pd.Series(close)
                volume = pd.Series(volume)
            
            typical_price = (high + low + close) / 3
            vwap = (typical_price * volume).cumsum() / volume.cumsum()
            
            return vwap.values
        except Exception as e:
            self.logger.error(f"Failed to calculate VWAP: {e}")
            return np.full(len(close), np.nan)
    
    def calculate_all_indicators(
        self, 
        ohlcv_data: pd.DataFrame
    ) -> Dict[str, Any]:
        """Calculate all technical indicators for OHLCV data."""
        try:
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in ohlcv_data.columns for col in required_columns):
                raise ValueError(f"DataFrame must contain columns: {required_columns}")
            
            high = ohlcv_data['high']
            low = ohlcv_data['low']
            close = ohlcv_data['close']
            volume = ohlcv_data['volume']
            
            indicators = {}
            
            # Moving Averages
            indicators['sma_20'] = self.sma(close, 20)
            indicators['sma_50'] = self.sma(close, 50)
            indicators['ema_12'] = self.ema(close, 12)
            indicators['ema_26'] = self.ema(close, 26)
            
            # Momentum Indicators
            indicators['rsi'] = self.rsi(close)
            macd_data = self.macd(close)
            indicators.update(macd_data)
            
            # Volatility Indicators
            bb_data = self.bollinger_bands(close)
            indicators.update({f'bb_{k}': v for k, v in bb_data.items()})
            indicators['atr'] = self.atr(high, low, close)
            
            # Oscillators
            stoch_data = self.stochastic(high, low, close)
            indicators.update(stoch_data)
            indicators['cci'] = self.cci(high, low, close)
            indicators['williams_r'] = self.williams_r(high, low, close)
            
            # Trend Indicators
            adx_data = self.adx(high, low, close)
            indicators.update(adx_data)
            
            # Volume Indicators
            indicators['obv'] = self.obv(close, volume)
            indicators['vwap'] = self.vwap(high, low, close, volume)
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Failed to calculate all indicators: {e}")
            return {}
    
    def generate_signals(
        self, 
        indicators: Dict[str, np.ndarray],
        ohlcv_data: pd.DataFrame
    ) -> Dict[str, Any]:
        """Generate trading signals based on technical indicators."""
        try:
            signals = {
                'overall_signal': 'NEUTRAL',
                'signal_strength': 0.0,
                'individual_signals': {}
            }
            
            close = ohlcv_data['close'].values
            current_price = close[-1] if len(close) > 0 else 0
            
            signal_scores = []
            
            # RSI Signal
            if 'rsi' in indicators and len(indicators['rsi']) > 0:
                rsi_current = indicators['rsi'][-1]
                if not np.isnan(rsi_current):
                    if rsi_current < 30:
                        signals['individual_signals']['rsi'] = {'signal': 'BUY', 'strength': 0.8}
                        signal_scores.append(0.8)
                    elif rsi_current > 70:
                        signals['individual_signals']['rsi'] = {'signal': 'SELL', 'strength': 0.8}
                        signal_scores.append(-0.8)
                    else:
                        signals['individual_signals']['rsi'] = {'signal': 'NEUTRAL', 'strength': 0.0}
                        signal_scores.append(0.0)
            
            # MACD Signal
            if 'macd' in indicators and 'signal' in indicators:
                macd_current = indicators['macd'][-1] if len(indicators['macd']) > 0 else np.nan
                signal_current = indicators['signal'][-1] if len(indicators['signal']) > 0 else np.nan
                
                if not (np.isnan(macd_current) or np.isnan(signal_current)):
                    if macd_current > signal_current:
                        signals['individual_signals']['macd'] = {'signal': 'BUY', 'strength': 0.6}
                        signal_scores.append(0.6)
                    else:
                        signals['individual_signals']['macd'] = {'signal': 'SELL', 'strength': 0.6}
                        signal_scores.append(-0.6)
            
            # Bollinger Bands Signal
            if 'bb_upper' in indicators and 'bb_lower' in indicators:
                bb_upper = indicators['bb_upper'][-1] if len(indicators['bb_upper']) > 0 else np.nan
                bb_lower = indicators['bb_lower'][-1] if len(indicators['bb_lower']) > 0 else np.nan
                
                if not (np.isnan(bb_upper) or np.isnan(bb_lower)):
                    if current_price <= bb_lower:
                        signals['individual_signals']['bollinger'] = {'signal': 'BUY', 'strength': 0.7}
                        signal_scores.append(0.7)
                    elif current_price >= bb_upper:
                        signals['individual_signals']['bollinger'] = {'signal': 'SELL', 'strength': 0.7}
                        signal_scores.append(-0.7)
                    else:
                        signals['individual_signals']['bollinger'] = {'signal': 'NEUTRAL', 'strength': 0.0}
                        signal_scores.append(0.0)
            
            # Moving Average Signal
            if 'sma_20' in indicators and 'sma_50' in indicators:
                sma_20 = indicators['sma_20'][-1] if len(indicators['sma_20']) > 0 else np.nan
                sma_50 = indicators['sma_50'][-1] if len(indicators['sma_50']) > 0 else np.nan
                
                if not (np.isnan(sma_20) or np.isnan(sma_50)):
                    if sma_20 > sma_50 and current_price > sma_20:
                        signals['individual_signals']['moving_average'] = {'signal': 'BUY', 'strength': 0.5}
                        signal_scores.append(0.5)
                    elif sma_20 < sma_50 and current_price < sma_20:
                        signals['individual_signals']['moving_average'] = {'signal': 'SELL', 'strength': 0.5}
                        signal_scores.append(-0.5)
                    else:
                        signals['individual_signals']['moving_average'] = {'signal': 'NEUTRAL', 'strength': 0.0}
                        signal_scores.append(0.0)
            
            # Calculate overall signal
            if signal_scores:
                avg_score = np.mean(signal_scores)
                signals['signal_strength'] = abs(avg_score)
                
                if avg_score > 0.3:
                    signals['overall_signal'] = 'BUY'
                elif avg_score < -0.3:
                    signals['overall_signal'] = 'SELL'
                else:
                    signals['overall_signal'] = 'NEUTRAL'
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Failed to generate signals: {e}")
            return {'overall_signal': 'NEUTRAL', 'signal_strength': 0.0, 'individual_signals': {}}


# Global technical indicators instance
technical_indicators = TechnicalIndicators()
