"""
Logging Configuration for QuantEdgeFlow
Structured logging with multiple handlers and formatters
"""

import logging
import logging.handlers
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import structlog
from pythonjsonlogger import jsonlogger

from src.config.settings import get_settings


settings = get_settings()


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with structured data."""
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add extra fields if present
        if hasattr(record, 'user_id'):
            log_data['user_id'] = record.user_id
        
        if hasattr(record, 'trade_id'):
            log_data['trade_id'] = record.trade_id
        
        if hasattr(record, 'symbol'):
            log_data['symbol'] = record.symbol
        
        if hasattr(record, 'request_id'):
            log_data['request_id'] = record.request_id
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_data, default=str)


class TradingLogFilter(logging.Filter):
    """Custom filter for trading-related logs."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter trading-related log records."""
        # Add trading context if available
        if hasattr(record, 'symbol'):
            record.trading_context = True
        
        return True


def setup_logging() -> None:
    """Setup comprehensive logging configuration."""
    
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    if settings.DEBUG:
        # Use simple format for development
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        # Use structured format for production
        console_formatter = StructuredFormatter()
    
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(TradingLogFilter())
    root_logger.addHandler(console_handler)
    
    # File handler for general logs
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "quantedgeflow.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(StructuredFormatter())
    file_handler.addFilter(TradingLogFilter())
    root_logger.addHandler(file_handler)
    
    # Separate handler for trading logs
    trading_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "trading.log",
        maxBytes=50 * 1024 * 1024,  # 50MB
        backupCount=10,
        encoding='utf-8'
    )
    trading_handler.setLevel(logging.INFO)
    trading_handler.setFormatter(StructuredFormatter())
    
    # Create trading logger
    trading_logger = logging.getLogger('trading')
    trading_logger.addHandler(trading_handler)
    trading_logger.setLevel(logging.INFO)
    
    # Error handler for critical errors
    error_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "errors.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(StructuredFormatter())
    
    # Add error handler to root logger
    root_logger.addHandler(error_handler)
    
    # Performance logger
    performance_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "performance.log",
        maxBytes=20 * 1024 * 1024,  # 20MB
        backupCount=5,
        encoding='utf-8'
    )
    performance_handler.setLevel(logging.INFO)
    performance_handler.setFormatter(StructuredFormatter())
    
    performance_logger = logging.getLogger('performance')
    performance_logger.addHandler(performance_handler)
    performance_logger.setLevel(logging.INFO)
    
    # Audit logger for compliance
    audit_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "audit.log",
        maxBytes=100 * 1024 * 1024,  # 100MB
        backupCount=20,
        encoding='utf-8'
    )
    audit_handler.setLevel(logging.INFO)
    audit_handler.setFormatter(StructuredFormatter())
    
    audit_logger = logging.getLogger('audit')
    audit_logger.addHandler(audit_handler)
    audit_logger.setLevel(logging.INFO)
    
    # Configure third-party loggers
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('fastapi').setLevel(logging.INFO)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info("Logging system initialized", extra={
        'log_level': settings.LOG_LEVEL,
        'debug_mode': settings.DEBUG,
        'log_directory': str(log_dir.absolute())
    })


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name."""
    return logging.getLogger(name)


def get_trading_logger() -> logging.Logger:
    """Get the trading-specific logger."""
    return logging.getLogger('trading')


def get_performance_logger() -> logging.Logger:
    """Get the performance logger."""
    return logging.getLogger('performance')


def get_audit_logger() -> logging.Logger:
    """Get the audit logger for compliance."""
    return logging.getLogger('audit')


class LogContext:
    """Context manager for adding structured logging context."""
    
    def __init__(self, **context):
        self.context = context
        self.logger = structlog.get_logger()
    
    def __enter__(self):
        self.bound_logger = self.logger.bind(**self.context)
        return self.bound_logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


def log_trade_execution(trade_id: str, symbol: str, action: str, **kwargs):
    """Log trade execution with structured data."""
    trading_logger = get_trading_logger()
    trading_logger.info(
        f"Trade {action}",
        extra={
            'trade_id': trade_id,
            'symbol': symbol,
            'action': action,
            **kwargs
        }
    )


def log_performance_metric(metric_name: str, value: float, **kwargs):
    """Log performance metrics."""
    performance_logger = get_performance_logger()
    performance_logger.info(
        f"Performance metric: {metric_name}",
        extra={
            'metric_name': metric_name,
            'metric_value': value,
            **kwargs
        }
    )


def log_audit_event(event_type: str, user_id: str, details: Dict[str, Any]):
    """Log audit events for compliance."""
    audit_logger = get_audit_logger()
    audit_logger.info(
        f"Audit event: {event_type}",
        extra={
            'event_type': event_type,
            'user_id': user_id,
            'details': details,
            'timestamp': datetime.utcnow().isoformat()
        }
    )
