"""
QuantEdgeFlow - Advanced Options Trading Platform
Main application entry point
"""

import asyncio
import logging
import sys
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import time

# Import configuration and setup
from config.settings import get_settings
from config.database import init_database, close_database
from config.logging_config import setup_logging

# Import API routes
from api.routes import (
    auth, portfolios, trades, options, analytics, 
    market_data, users, notifications
)

# Import middleware
from api.middleware.auth import AuthMiddleware
from api.middleware.rate_limiting import RateLimitMiddleware
from api.middleware.request_logging import RequestLoggingMiddleware

# Import services
from services.notification_service import notification_service
from data.collectors.market_data import market_data_collector

# Import utilities
from utils.constants import API_PREFIX
from utils.logger import get_audit_logger


# Initialize settings and logging
settings = get_settings()
setup_logging()
logger = logging.getLogger(__name__)
audit_logger = get_audit_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting QuantEdgeFlow application...")
    
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized successfully")
        
        # Start background services
        if settings.ENABLE_MARKET_DATA_COLLECTION:
            # Start market data collection in background
            asyncio.create_task(start_market_data_collection())
            logger.info("Market data collection started")
        
        # Initialize notification service
        logger.info("Notification service initialized")
        
        audit_logger.info("Application startup completed", extra={
            "environment": settings.ENVIRONMENT,
            "debug_mode": settings.DEBUG,
            "database_url": settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else "***"
        })
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)
    
    # Shutdown
    logger.info("Shutting down QuantEdgeFlow application...")
    
    try:
        # Close database connections
        await close_database()
        logger.info("Database connections closed")
        
        # Stop background services
        # Additional cleanup would go here
        
        audit_logger.info("Application shutdown completed")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="QuantEdgeFlow API",
    description="Advanced Options Trading Platform with Real-time Analytics",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_url="/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(AuthMiddleware)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", extra={
        "path": request.url.path,
        "method": request.method,
        "client_ip": request.client.host if request.client else None
    })
    
    if settings.DEBUG:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "detail": str(exc),
                "type": type(exc).__name__
            }
        )
    else:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred"
            }
        )


# HTTP exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP exception handler."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code
        }
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "QuantEdgeFlow API",
        "version": "1.0.0",
        "docs_url": "/docs" if settings.DEBUG else None,
        "health_url": "/health"
    }


# Include API routes
app.include_router(auth.router, prefix=f"{API_PREFIX}/auth", tags=["Authentication"])
app.include_router(users.router, prefix=f"{API_PREFIX}/users", tags=["Users"])
app.include_router(portfolios.router, prefix=f"{API_PREFIX}/portfolios", tags=["Portfolios"])
app.include_router(trades.router, prefix=f"{API_PREFIX}/trades", tags=["Trades"])
app.include_router(options.router, prefix=f"{API_PREFIX}/options", tags=["Options"])
app.include_router(analytics.router, prefix=f"{API_PREFIX}/analytics", tags=["Analytics"])
app.include_router(market_data.router, prefix=f"{API_PREFIX}/market-data", tags=["Market Data"])
app.include_router(notifications.router, prefix=f"{API_PREFIX}/notifications", tags=["Notifications"])


async def start_market_data_collection():
    """Start background market data collection."""
    try:
        # List of symbols to collect data for
        symbols = [
            "SPY", "QQQ", "IWM", "VIX",  # Market indices
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA",  # Major stocks
            "NVDA", "META", "NFLX", "AMD", "CRM"  # Tech stocks
        ]
        
        while True:
            try:
                async with market_data_collector as collector:
                    # Collect quotes for all symbols
                    quotes = await collector.get_multiple_quotes(symbols)
                    
                    if quotes:
                        logger.info(f"Collected quotes for {len(quotes)} symbols")
                    
                    # Wait before next collection
                    await asyncio.sleep(settings.MARKET_DATA_REFRESH_INTERVAL)
                    
            except Exception as e:
                logger.error(f"Error in market data collection: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
                
    except Exception as e:
        logger.error(f"Failed to start market data collection: {e}")


def main():
    """Main function to run the application."""
    try:
        # Log startup information
        logger.info(f"Starting QuantEdgeFlow in {settings.ENVIRONMENT} mode")
        logger.info(f"Debug mode: {settings.DEBUG}")
        logger.info(f"API documentation: {'Enabled' if settings.DEBUG else 'Disabled'}")
        
        # Run the application
        uvicorn.run(
            "main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info" if not settings.DEBUG else "debug",
            access_log=True,
            server_header=False,
            date_header=False
        )
        
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
