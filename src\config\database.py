"""
Database Configuration and Connection Management
Advanced PostgreSQL setup with connection pooling and async support
"""

import asyncio
import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

import asyncpg
from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.asyncio import (
    AsyncSession, 
    create_async_engine, 
    async_sessionmaker,
    AsyncEngine
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import NullPool, QueuePool

from src.config.settings import get_settings


settings = get_settings()
logger = logging.getLogger(__name__)

# SQLAlchemy Base
Base = declarative_base()
metadata = MetaData()

# Global engine and session variables
async_engine: Optional[AsyncEngine] = None
async_session_factory: Optional[async_sessionmaker] = None
sync_engine = None
sync_session_factory = None


class DatabaseManager:
    """Advanced database connection manager with pooling and monitoring."""
    
    def __init__(self):
        self.async_engine: Optional[AsyncEngine] = None
        self.sync_engine = None
        self.async_session_factory: Optional[async_sessionmaker] = None
        self.sync_session_factory = None
        self._connection_pool_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "idle_connections": 0
        }
    
    async def initialize_async_engine(self) -> AsyncEngine:
        """Initialize async database engine with optimized settings."""
        if self.async_engine is not None:
            return self.async_engine
        
        # Configure connection parameters
        connect_args = {
            "server_settings": {
                "application_name": "QuantEdgeFlow",
                "jit": "off",  # Disable JIT for better performance on small queries
            },
            "command_timeout": 60,
            "statement_cache_size": 0,  # Disable prepared statement cache
        }
        
        # Create async engine with connection pooling
        self.async_engine = create_async_engine(
            settings.DATABASE_URL,
            echo=settings.DEBUG,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections every hour
            connect_args=connect_args,
            future=True
        )
        
        # Add connection event listeners
        @event.listens_for(self.async_engine.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """Set database-specific optimizations."""
            if hasattr(dbapi_connection, 'execute'):
                # PostgreSQL optimizations
                dbapi_connection.execute("SET timezone = 'UTC'")
                dbapi_connection.execute("SET statement_timeout = '60s'")
                dbapi_connection.execute("SET lock_timeout = '30s'")
        
        # Create session factory
        self.async_session_factory = async_sessionmaker(
            bind=self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False
        )
        
        logger.info("Async database engine initialized", extra={
            "pool_size": settings.DATABASE_POOL_SIZE,
            "max_overflow": settings.DATABASE_MAX_OVERFLOW,
            "database_url": settings.DATABASE_URL.split("@")[0] + "@***"  # Hide credentials
        })
        
        return self.async_engine
    
    def initialize_sync_engine(self):
        """Initialize synchronous database engine for migrations and admin tasks."""
        if self.sync_engine is not None:
            return self.sync_engine
        
        # Convert async URL to sync URL
        sync_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        
        self.sync_engine = create_engine(
            sync_url,
            echo=settings.DEBUG,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        self.sync_session_factory = sessionmaker(
            bind=self.sync_engine,
            autoflush=True,
            autocommit=False
        )
        
        logger.info("Sync database engine initialized")
        return self.sync_engine
    
    async def get_connection_stats(self) -> dict:
        """Get current connection pool statistics."""
        if not self.async_engine:
            return {"status": "not_initialized"}
        
        pool = self.async_engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    
    async def health_check(self) -> bool:
        """Perform database health check."""
        try:
            if not self.async_engine:
                return False
            
            async with self.async_engine.begin() as conn:
                result = await conn.execute("SELECT 1")
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def close(self):
        """Close all database connections."""
        if self.async_engine:
            await self.async_engine.dispose()
            logger.info("Async database engine disposed")
        
        if self.sync_engine:
            self.sync_engine.dispose()
            logger.info("Sync database engine disposed")


# Global database manager instance
db_manager = DatabaseManager()


async def init_database() -> None:
    """Initialize database connections and create tables."""
    global async_engine, async_session_factory, sync_engine, sync_session_factory
    
    try:
        # Initialize engines
        async_engine = await db_manager.initialize_async_engine()
        sync_engine = db_manager.initialize_sync_engine()
        
        # Set global session factories
        async_session_factory = db_manager.async_session_factory
        sync_session_factory = db_manager.sync_session_factory
        
        # Create tables if they don't exist
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


async def close_database() -> None:
    """Close database connections."""
    await db_manager.close()


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session with automatic cleanup."""
    if not async_session_factory:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    async with async_session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


def get_sync_session() -> Session:
    """Get synchronous database session."""
    if not sync_session_factory:
        raise RuntimeError("Sync database not initialized.")
    
    return sync_session_factory()


async def execute_raw_query(query: str, params: dict = None) -> list:
    """Execute raw SQL query with parameters."""
    async with get_async_session() as session:
        result = await session.execute(query, params or {})
        return result.fetchall()


class DatabaseTransaction:
    """Context manager for database transactions with rollback support."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.transaction = None
    
    async def __aenter__(self):
        self.transaction = await self.session.begin()
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.transaction.rollback()
            logger.warning(f"Transaction rolled back due to: {exc_val}")
        else:
            await self.transaction.commit()


# Database dependency for FastAPI
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for database sessions."""
    async with get_async_session() as session:
        yield session


# Connection monitoring utilities
async def monitor_connections():
    """Monitor database connection health."""
    while True:
        try:
            stats = await db_manager.get_connection_stats()
            health = await db_manager.health_check()

            logger.info("Database monitoring", extra={
                "connection_stats": stats,
                "health_status": health
            })

            # Alert if connection pool is getting full
            if stats.get("checked_out", 0) > settings.DATABASE_POOL_SIZE * 0.8:
                logger.warning("Database connection pool usage high", extra=stats)

        except Exception as e:
            logger.error(f"Database monitoring error: {e}")

        await asyncio.sleep(60)  # Check every minute
