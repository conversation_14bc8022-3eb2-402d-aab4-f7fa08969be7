"""
Options API Routes for QuantEdgeFlow
RESTful endpoints for options analysis, pricing, and chain management
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, date

from core.models.options import (
    OptionChainRequest, OptionChainResponse, OptionContractResponse,
    GreeksCalculationRequest, GreeksResponse, VolatilitySurfaceResponse
)
from services.options_service import OptionsService
from api.middleware.auth import get_current_user, require_permission, Permission
from utils.helpers import validate_symbol


logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/chain/{symbol}", response_model=OptionChainResponse)
async def get_option_chain(
    symbol: str = Path(..., description="Stock symbol"),
    expiration_date: Optional[date] = Query(None, description="Specific expiration date"),
    min_days_to_expiration: Optional[int] = Query(None, ge=0, description="Minimum days to expiration"),
    max_days_to_expiration: Optional[int] = Query(None, ge=0, description="Maximum days to expiration"),
    min_strike: Optional[float] = Query(None, gt=0, description="Minimum strike price"),
    max_strike: Optional[float] = Query(None, gt=0, description="Maximum strike price"),
    option_type: Optional[str] = Query("both", regex="^(call|put|both)$", description="Option type filter"),
    min_volume: Optional[int] = Query(None, ge=0, description="Minimum volume filter"),
    min_open_interest: Optional[int] = Query(None, ge=0, description="Minimum open interest filter"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    options_service: OptionsService = Depends()
):
    """Get option chain for a symbol with filtering options."""
    try:
        validate_symbol(symbol)
        
        # Create request object
        request = OptionChainRequest(
            symbol=symbol,
            expiration_date=expiration_date,
            min_days_to_expiration=min_days_to_expiration,
            max_days_to_expiration=max_days_to_expiration,
            min_strike=min_strike,
            max_strike=max_strike,
            option_type=option_type,
            min_volume=min_volume,
            min_open_interest=min_open_interest
        )
        
        chain = await options_service.get_option_chain(request)
        
        if not chain:
            raise HTTPException(
                status_code=404,
                detail=f"Option chain not found for symbol {symbol}"
            )
        
        logger.info(f"Option chain retrieved", extra={
            "user_id": user_id,
            "symbol": symbol,
            "contracts_count": len(chain.calls) + len(chain.puts)
        })
        
        return chain
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get option chain: {e}", extra={
            "user_id": user_id,
            "symbol": symbol
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/greeks", response_model=GreeksResponse)
async def calculate_greeks(
    request: GreeksCalculationRequest,
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    options_service: OptionsService = Depends()
):
    """Calculate option Greeks and theoretical pricing."""
    try:
        validate_symbol(request.symbol)
        
        greeks = await options_service.calculate_greeks(request)
        
        logger.info(f"Greeks calculated", extra={
            "user_id": user_id,
            "symbol": request.symbol,
            "option_type": request.option_type,
            "strike": float(request.strike_price),
            "expiration": request.expiration_date.isoformat()
        })
        
        return greeks
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to calculate Greeks: {e}", extra={
            "user_id": user_id,
            "symbol": request.symbol
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/volatility-surface/{symbol}", response_model=VolatilitySurfaceResponse)
async def get_volatility_surface(
    symbol: str = Path(..., description="Stock symbol"),
    calculation_date: Optional[date] = Query(None, description="Calculation date (default: today)"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    options_service: OptionsService = Depends()
):
    """Get volatility surface for a symbol."""
    try:
        validate_symbol(symbol)
        
        surface = await options_service.get_volatility_surface(
            symbol, 
            calculation_date
        )
        
        if not surface:
            raise HTTPException(
                status_code=404,
                detail=f"Volatility surface not found for symbol {symbol}"
            )
        
        logger.info(f"Volatility surface retrieved", extra={
            "user_id": user_id,
            "symbol": symbol,
            "calculation_date": surface.calculation_date.isoformat()
        })
        
        return surface
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get volatility surface: {e}", extra={
            "user_id": user_id,
            "symbol": symbol
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/contracts/{symbol}", response_model=List[OptionContractResponse])
async def get_option_contracts(
    symbol: str = Path(..., description="Stock symbol"),
    expiration_date: Optional[date] = Query(None, description="Expiration date filter"),
    option_type: Optional[str] = Query(None, regex="^(call|put)$", description="Option type filter"),
    min_strike: Optional[float] = Query(None, gt=0, description="Minimum strike price"),
    max_strike: Optional[float] = Query(None, gt=0, description="Maximum strike price"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of contracts to return"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    options_service: OptionsService = Depends()
):
    """Get option contracts with filtering."""
    try:
        validate_symbol(symbol)
        
        # Create request for option chain
        request = OptionChainRequest(
            symbol=symbol,
            expiration_date=expiration_date,
            option_type=option_type or "both",
            min_strike=min_strike,
            max_strike=max_strike
        )
        
        chain = await options_service.get_option_chain(request)
        
        if not chain:
            return []
        
        # Combine calls and puts
        contracts = []
        if option_type is None or option_type == "call":
            contracts.extend(chain.calls)
        if option_type is None or option_type == "put":
            contracts.extend(chain.puts)
        
        # Apply limit
        contracts = contracts[:limit]
        
        logger.info(f"Option contracts retrieved", extra={
            "user_id": user_id,
            "symbol": symbol,
            "contracts_count": len(contracts)
        })
        
        return contracts
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get option contracts: {e}", extra={
            "user_id": user_id,
            "symbol": symbol
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/analysis/{symbol}", response_model=Dict[str, Any])
async def get_options_analysis(
    symbol: str = Path(..., description="Stock symbol"),
    expiration_date: Optional[date] = Query(None, description="Expiration date for analysis"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    options_service: OptionsService = Depends()
):
    """Get comprehensive options analysis for a symbol."""
    try:
        validate_symbol(symbol)
        
        # Get option chain
        request = OptionChainRequest(
            symbol=symbol,
            expiration_date=expiration_date,
            option_type="both"
        )
        
        chain = await options_service.get_option_chain(request)
        
        if not chain:
            raise HTTPException(
                status_code=404,
                detail=f"Options data not found for symbol {symbol}"
            )
        
        # Calculate analysis metrics
        analysis = {
            "symbol": symbol,
            "underlying_price": chain.underlying_price,
            "expiration_date": chain.expiration_date,
            "total_volume": chain.total_call_volume + chain.total_put_volume,
            "total_open_interest": chain.total_call_open_interest + chain.total_put_open_interest,
            "put_call_ratio": chain.put_call_ratio,
            "max_pain": chain.max_pain,
            "gamma_exposure": chain.gamma_exposure,
            "implied_volatility_rank": chain.implied_volatility_rank,
            "implied_volatility_percentile": chain.implied_volatility_percentile,
            "historical_volatility": chain.historical_volatility,
            "call_statistics": {
                "count": len(chain.calls),
                "total_volume": chain.total_call_volume,
                "total_open_interest": chain.total_call_open_interest,
                "avg_implied_volatility": _calculate_avg_iv(chain.calls) if chain.calls else None
            },
            "put_statistics": {
                "count": len(chain.puts),
                "total_volume": chain.total_put_volume,
                "total_open_interest": chain.total_put_open_interest,
                "avg_implied_volatility": _calculate_avg_iv(chain.puts) if chain.puts else None
            },
            "strike_analysis": _analyze_strikes(chain.calls + chain.puts, chain.underlying_price),
            "volume_analysis": _analyze_volume(chain.calls + chain.puts),
            "calculated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Options analysis completed", extra={
            "user_id": user_id,
            "symbol": symbol,
            "total_contracts": len(chain.calls) + len(chain.puts)
        })
        
        return analysis
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get options analysis: {e}", extra={
            "user_id": user_id,
            "symbol": symbol
        })
        raise HTTPException(status_code=500, detail="Internal server error")


def _calculate_avg_iv(contracts: List[OptionContractResponse]) -> Optional[float]:
    """Calculate average implied volatility."""
    if not contracts:
        return None
    
    ivs = [c.implied_volatility for c in contracts if c.implied_volatility is not None]
    return sum(ivs) / len(ivs) if ivs else None


def _analyze_strikes(contracts: List[OptionContractResponse], underlying_price: Optional[float]) -> Dict[str, Any]:
    """Analyze strike price distribution."""
    if not contracts or not underlying_price:
        return {}
    
    strikes = [float(c.strike_price) for c in contracts]
    
    return {
        "min_strike": min(strikes),
        "max_strike": max(strikes),
        "strike_count": len(set(strikes)),
        "atm_strike": min(strikes, key=lambda x: abs(x - underlying_price)),
        "strike_spacing": _calculate_strike_spacing(strikes)
    }


def _calculate_strike_spacing(strikes: List[float]) -> Optional[float]:
    """Calculate average strike spacing."""
    if len(strikes) < 2:
        return None
    
    sorted_strikes = sorted(set(strikes))
    spacings = [sorted_strikes[i+1] - sorted_strikes[i] for i in range(len(sorted_strikes)-1)]
    
    return sum(spacings) / len(spacings) if spacings else None


def _analyze_volume(contracts: List[OptionContractResponse]) -> Dict[str, Any]:
    """Analyze volume distribution."""
    if not contracts:
        return {}
    
    volumes = [c.volume for c in contracts]
    total_volume = sum(volumes)
    
    return {
        "total_volume": total_volume,
        "avg_volume": total_volume / len(volumes) if volumes else 0,
        "max_volume": max(volumes) if volumes else 0,
        "contracts_with_volume": len([v for v in volumes if v > 0]),
        "volume_concentration": _calculate_volume_concentration(volumes)
    }


def _calculate_volume_concentration(volumes: List[int]) -> float:
    """Calculate volume concentration (top 10% of contracts by volume)."""
    if not volumes:
        return 0.0
    
    sorted_volumes = sorted(volumes, reverse=True)
    top_10_percent_count = max(1, len(sorted_volumes) // 10)
    top_volume = sum(sorted_volumes[:top_10_percent_count])
    total_volume = sum(sorted_volumes)
    
    return (top_volume / total_volume * 100) if total_volume > 0 else 0.0
