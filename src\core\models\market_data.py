"""
Market Data Models for QuantEdgeFlow
Comprehensive market data storage and real-time price tracking
"""

from datetime import datetime, date, time
from decimal import Decimal
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid

from sqlalchemy import (
    Column, String, Float, Integer, DateTime, Boolean, 
    Text, ForeignKey, Numeric, Date, Time, JSON, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, validates
from pydantic import BaseModel, Field, validator
from pydantic.types import condecimal

from src.config.database import Base


class MarketStatus(str, Enum):
    """Market status enumeration."""
    PRE_MARKET = "pre_market"
    OPEN = "open"
    CLOSED = "closed"
    AFTER_HOURS = "after_hours"
    HOLIDAY = "holiday"


class DataSource(str, Enum):
    """Data source enumeration."""
    YAHOO_FINANCE = "yahoo_finance"
    ALPHA_VANTAGE = "alpha_vantage"
    POLYGON = "polygon"
    IEX = "iex"
    QUANDL = "quandl"
    INTERNAL = "internal"


class QuoteType(str, Enum):
    """Quote type enumeration."""
    REAL_TIME = "real_time"
    DELAYED = "delayed"
    END_OF_DAY = "end_of_day"


class StockQuote(Base):
    """Real-time stock quote model."""
    
    __tablename__ = "stock_quotes"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Symbol identification
    symbol = Column(String(20), nullable=False, index=True)
    company_name = Column(String(255))
    exchange = Column(String(10))
    
    # Price data
    price = Column(Numeric(10, 4), nullable=False)
    open_price = Column(Numeric(10, 4))
    high_price = Column(Numeric(10, 4))
    low_price = Column(Numeric(10, 4))
    previous_close = Column(Numeric(10, 4))
    
    # Change metrics
    change = Column(Numeric(10, 4))
    change_percent = Column(Float)
    
    # Volume data
    volume = Column(Integer, default=0)
    average_volume = Column(Integer)
    volume_ratio = Column(Float)  # Current volume / average volume
    
    # Bid/Ask data
    bid = Column(Numeric(10, 4))
    ask = Column(Numeric(10, 4))
    bid_size = Column(Integer)
    ask_size = Column(Integer)
    spread = Column(Numeric(10, 4))
    spread_percent = Column(Float)
    
    # Market cap and valuation
    market_cap = Column(Numeric(20, 2))
    shares_outstanding = Column(Integer)
    float_shares = Column(Integer)
    
    # Technical indicators
    day_range_low = Column(Numeric(10, 4))
    day_range_high = Column(Numeric(10, 4))
    week_52_low = Column(Numeric(10, 4))
    week_52_high = Column(Numeric(10, 4))
    
    # Volatility metrics
    historical_volatility = Column(Float)
    beta = Column(Float)
    
    # Metadata
    data_source = Column(String(20), default=DataSource.YAHOO_FINANCE)
    quote_type = Column(String(20), default=QuoteType.REAL_TIME)
    market_status = Column(String(20))
    
    # Timestamps
    quote_time = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_stock_symbol_time', 'symbol', 'quote_time'),
        Index('idx_stock_updated', 'updated_at'),
    )
    
    def calculate_spread_percent(self) -> float:
        """Calculate bid-ask spread percentage."""
        if not self.bid or not self.ask or self.price == 0:
            return 0.0
        spread = float(self.ask - self.bid)
        return (spread / float(self.price)) * 100


class HistoricalPrice(Base):
    """Historical price data model."""
    
    __tablename__ = "historical_prices"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Symbol and date
    symbol = Column(String(20), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)
    
    # OHLCV data
    open_price = Column(Numeric(10, 4), nullable=False)
    high_price = Column(Numeric(10, 4), nullable=False)
    low_price = Column(Numeric(10, 4), nullable=False)
    close_price = Column(Numeric(10, 4), nullable=False)
    adjusted_close = Column(Numeric(10, 4))
    volume = Column(Integer, default=0)
    
    # Calculated metrics
    change = Column(Numeric(10, 4))
    change_percent = Column(Float)
    vwap = Column(Numeric(10, 4))  # Volume Weighted Average Price
    
    # Technical indicators
    sma_20 = Column(Numeric(10, 4))  # 20-day Simple Moving Average
    sma_50 = Column(Numeric(10, 4))  # 50-day Simple Moving Average
    sma_200 = Column(Numeric(10, 4))  # 200-day Simple Moving Average
    ema_12 = Column(Numeric(10, 4))  # 12-day Exponential Moving Average
    ema_26 = Column(Numeric(10, 4))  # 26-day Exponential Moving Average
    
    # Volatility
    daily_return = Column(Float)
    volatility_20d = Column(Float)  # 20-day rolling volatility
    
    # Metadata
    data_source = Column(String(20), default=DataSource.YAHOO_FINANCE)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Unique constraint
    __table_args__ = (
        Index('idx_historical_symbol_date', 'symbol', 'date', unique=True),
        Index('idx_historical_date', 'date'),
    )


class IntradayPrice(Base):
    """Intraday price data model."""
    
    __tablename__ = "intraday_prices"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Symbol and timestamp
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Price data
    open_price = Column(Numeric(10, 4))
    high_price = Column(Numeric(10, 4))
    low_price = Column(Numeric(10, 4))
    close_price = Column(Numeric(10, 4), nullable=False)
    volume = Column(Integer, default=0)
    
    # Interval information
    interval = Column(String(10))  # 1m, 5m, 15m, 1h, etc.
    
    # Metadata
    data_source = Column(String(20), default=DataSource.YAHOO_FINANCE)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        Index('idx_intraday_symbol_timestamp', 'symbol', 'timestamp'),
        Index('idx_intraday_interval', 'interval'),
    )


class MarketHours(Base):
    """Market hours and trading calendar."""
    
    __tablename__ = "market_hours"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Date and exchange
    date = Column(Date, nullable=False, index=True)
    exchange = Column(String(10), nullable=False, default="NYSE")
    
    # Market hours
    pre_market_open = Column(Time)
    market_open = Column(Time)
    market_close = Column(Time)
    after_hours_close = Column(Time)
    
    # Status
    is_trading_day = Column(Boolean, default=True)
    is_holiday = Column(Boolean, default=False)
    holiday_name = Column(String(100))
    
    # Early close information
    is_early_close = Column(Boolean, default=False)
    early_close_time = Column(Time)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Unique constraint
    __table_args__ = (
        Index('idx_market_hours_date_exchange', 'date', 'exchange', unique=True),
    )


class EconomicIndicator(Base):
    """Economic indicators and events."""
    
    __tablename__ = "economic_indicators"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Indicator details
    name = Column(String(255), nullable=False)
    code = Column(String(50), nullable=False, index=True)
    category = Column(String(100))
    country = Column(String(10), default="US")
    
    # Data
    date = Column(Date, nullable=False, index=True)
    value = Column(Numeric(20, 6))
    previous_value = Column(Numeric(20, 6))
    forecast_value = Column(Numeric(20, 6))
    
    # Change metrics
    change = Column(Numeric(20, 6))
    change_percent = Column(Float)
    
    # Importance and impact
    importance = Column(String(10))  # low, medium, high
    market_impact = Column(String(10))  # positive, negative, neutral
    
    # Metadata
    unit = Column(String(50))
    frequency = Column(String(20))  # daily, weekly, monthly, quarterly, annual
    data_source = Column(String(20))
    
    # Timestamps
    release_time = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        Index('idx_economic_code_date', 'code', 'date'),
        Index('idx_economic_category', 'category'),
    )


# Pydantic models for API
class StockQuoteResponse(BaseModel):
    """Stock quote response model."""
    symbol: str
    company_name: Optional[str] = None
    exchange: Optional[str] = None
    price: Decimal
    open_price: Optional[Decimal] = None
    high_price: Optional[Decimal] = None
    low_price: Optional[Decimal] = None
    previous_close: Optional[Decimal] = None
    change: Optional[Decimal] = None
    change_percent: Optional[float] = None
    volume: int = 0
    average_volume: Optional[int] = None
    volume_ratio: Optional[float] = None
    bid: Optional[Decimal] = None
    ask: Optional[Decimal] = None
    bid_size: Optional[int] = None
    ask_size: Optional[int] = None
    spread: Optional[Decimal] = None
    spread_percent: Optional[float] = None
    market_cap: Optional[Decimal] = None
    shares_outstanding: Optional[int] = None
    day_range_low: Optional[Decimal] = None
    day_range_high: Optional[Decimal] = None
    week_52_low: Optional[Decimal] = None
    week_52_high: Optional[Decimal] = None
    historical_volatility: Optional[float] = None
    beta: Optional[float] = None
    data_source: str
    quote_type: str
    market_status: Optional[str] = None
    quote_time: datetime
    
    class Config:
        from_attributes = True


class HistoricalPriceResponse(BaseModel):
    """Historical price response model."""
    symbol: str
    date: date
    open_price: Decimal
    high_price: Decimal
    low_price: Decimal
    close_price: Decimal
    adjusted_close: Optional[Decimal] = None
    volume: int = 0
    change: Optional[Decimal] = None
    change_percent: Optional[float] = None
    vwap: Optional[Decimal] = None
    sma_20: Optional[Decimal] = None
    sma_50: Optional[Decimal] = None
    sma_200: Optional[Decimal] = None
    ema_12: Optional[Decimal] = None
    ema_26: Optional[Decimal] = None
    daily_return: Optional[float] = None
    volatility_20d: Optional[float] = None
    data_source: str
    
    class Config:
        from_attributes = True


class IntradayPriceResponse(BaseModel):
    """Intraday price response model."""
    symbol: str
    timestamp: datetime
    open_price: Optional[Decimal] = None
    high_price: Optional[Decimal] = None
    low_price: Optional[Decimal] = None
    close_price: Decimal
    volume: int = 0
    interval: Optional[str] = None
    data_source: str
    
    class Config:
        from_attributes = True


class MarketDataRequest(BaseModel):
    """Market data request model."""
    symbols: List[str] = Field(..., min_items=1, max_items=100)
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    interval: Optional[str] = Field(None, pattern="^(1m|5m|15m|30m|1h|1d)$")
    include_extended_hours: bool = False
    
    @validator('symbols')
    def validate_symbols(cls, v):
        """Validate symbol format."""
        for symbol in v:
            if not symbol.strip() or len(symbol) > 20:
                raise ValueError(f"Invalid symbol: {symbol}")
        return [s.upper().strip() for s in v]
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """Validate date range."""
        start_date = values.get('start_date')
        if start_date and v and v < start_date:
            raise ValueError('end_date must be >= start_date')
        return v


class MarketHoursResponse(BaseModel):
    """Market hours response model."""
    date: date
    exchange: str
    pre_market_open: Optional[time] = None
    market_open: Optional[time] = None
    market_close: Optional[time] = None
    after_hours_close: Optional[time] = None
    is_trading_day: bool
    is_holiday: bool
    holiday_name: Optional[str] = None
    is_early_close: bool
    early_close_time: Optional[time] = None
    
    class Config:
        from_attributes = True


class EconomicIndicatorResponse(BaseModel):
    """Economic indicator response model."""
    name: str
    code: str
    category: Optional[str] = None
    country: str = "US"
    date: date
    value: Optional[Decimal] = None
    previous_value: Optional[Decimal] = None
    forecast_value: Optional[Decimal] = None
    change: Optional[Decimal] = None
    change_percent: Optional[float] = None
    importance: Optional[str] = None
    market_impact: Optional[str] = None
    unit: Optional[str] = None
    frequency: Optional[str] = None
    release_time: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class MarketData(BaseModel):
    """General market data model."""
    symbol: str
    timestamp: datetime
    data_type: str
    data: Dict[str, Any]
    source: str


class DataSource(str, Enum):
    """Data source enumeration."""
    YAHOO_FINANCE = "yahoo_finance"
    ALPHA_VANTAGE = "alpha_vantage"
    POLYGON = "polygon"
    IEX = "iex"
    INTERNAL = "internal"


class QuoteType(str, Enum):
    """Quote type enumeration."""
    REAL_TIME = "real_time"
    DELAYED = "delayed"
    END_OF_DAY = "end_of_day"


class StockQuoteCreate(BaseModel):
    """Stock quote creation model."""
    symbol: str = Field(..., min_length=1, max_length=20)
    price: Decimal = Field(..., gt=0)
    bid: Optional[Decimal] = None
    ask: Optional[Decimal] = None
    volume: Optional[int] = None


class HistoricalPriceCreate(BaseModel):
    """Historical price creation model."""
    symbol: str = Field(..., min_length=1, max_length=20)
    date: date
    open_price: Decimal = Field(..., gt=0)
    high_price: Decimal = Field(..., gt=0)
    low_price: Decimal = Field(..., gt=0)
    close_price: Decimal = Field(..., gt=0)
    volume: Optional[int] = None


class MarketDataResponse(BaseModel):
    """Market data response model."""
    symbol: str
    data: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    generated_at: datetime



