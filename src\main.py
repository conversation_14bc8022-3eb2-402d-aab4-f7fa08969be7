"""
QuantEdgeFlow - Elite Options Trading Analysis Platform
Main application entry point with FastAPI server setup
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware

from config.settings import get_settings
from config.database import init_database, close_database
from api.routes import portfolio, options, analytics, trades
from api.middleware.auth import AuthMiddleware
from api.middleware.rate_limiting import RateLimitMiddleware
from services.portfolio_service import PortfolioService
from services.analytics_service import AnalyticsService
from data.collectors.market_data import MarketDataCollector
from utils.logger import setup_logging


settings = get_settings()
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager for startup and shutdown events."""
    logger.info("Starting QuantEdgeFlow application...")
    
    # Initialize database connections
    await init_database()
    
    # Initialize services
    app.state.portfolio_service = PortfolioService()
    app.state.analytics_service = AnalyticsService()
    app.state.market_data_collector = MarketDataCollector()
    
    # Start background tasks
    asyncio.create_task(app.state.market_data_collector.start_real_time_collection())
    
    logger.info("QuantEdgeFlow application started successfully")
    
    yield
    
    # Cleanup on shutdown
    logger.info("Shutting down QuantEdgeFlow application...")
    await app.state.market_data_collector.stop_collection()
    await close_database()
    logger.info("QuantEdgeFlow application shutdown complete")


def create_application() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title="QuantEdgeFlow API",
        description="Elite Options Trading Analysis Platform",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(AuthMiddleware)
    app.add_middleware(RateLimitMiddleware)
    
    # Include routers
    app.include_router(
        portfolio.router,
        prefix="/api/v1/portfolio",
        tags=["Portfolio Management"]
    )
    
    app.include_router(
        options.router,
        prefix="/api/v1/options",
        tags=["Options Analysis"]
    )
    
    app.include_router(
        analytics.router,
        prefix="/api/v1/analytics",
        tags=["Analytics & Risk"]
    )
    
    app.include_router(
        trades.router,
        prefix="/api/v1/trades",
        tags=["Trade Execution"]
    )
    
    return app


app = create_application()


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "QuantEdgeFlow API",
        "version": "1.0.0",
        "description": "Elite Options Trading Analysis Platform",
        "status": "operational",
        "endpoints": {
            "portfolio": "/api/v1/portfolio",
            "options": "/api/v1/options",
            "analytics": "/api/v1/analytics",
            "trades": "/api/v1/trades",
            "docs": "/docs",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    try:
        # Check database connectivity
        # Check external API connectivity
        # Check cache connectivity
        
        return {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "services": {
                "database": "operational",
                "cache": "operational",
                "market_data": "operational",
                "analytics": "operational"
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return HTTPException(
        status_code=500,
        detail="Internal server error"
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info",
        access_log=True
    )
