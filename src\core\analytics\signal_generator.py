"""
Signal Generation Engine for QuantEdgeFlow
Advanced signal generation for options trading using multiple data sources and ML models
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
from enum import Enum
import warnings

from src.core.analytics.technical_indicators import TechnicalIndicators
from src.core.analytics.risk_metrics import RiskMetricsCalculator
from src.core.algorithms.machine_learning import MLPredictor
from src.utils.constants import TRADING_DAYS_PER_YEAR

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


class SignalStrength(str, Enum):
    """Signal strength enumeration."""
    VERY_WEAK = "very_weak"
    WEAK = "weak"
    NEUTRAL = "neutral"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


class SignalDirection(str, Enum):
    """Signal direction enumeration."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"


@dataclass
class TradingSignal:
    """Trading signal data structure."""
    symbol: str
    signal_type: str
    direction: SignalDirection
    strength: SignalStrength
    confidence: float
    entry_price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    probability_of_profit: float
    expected_return: float
    risk_reward_ratio: float
    time_horizon: int  # Days
    generated_at: datetime
    expires_at: datetime
    metadata: Dict[str, Any]


@dataclass
class MarketRegime:
    """Market regime classification."""
    regime_type: str  # trending, ranging, volatile, calm
    volatility_level: str  # low, medium, high
    trend_direction: str  # up, down, sideways
    strength: float
    confidence: float


class SignalGenerator:
    """Comprehensive signal generation engine."""
    
    def __init__(self):
        self.logger = logger
        self.technical_indicators = TechnicalIndicators()
        self.risk_calculator = RiskMetricsCalculator()
        self.ml_predictor = MLPredictor()
        
        # Signal weights for different components
        self.signal_weights = {
            'technical': 0.30,
            'fundamental': 0.25,
            'sentiment': 0.20,
            'volatility': 0.15,
            'ml_prediction': 0.10
        }
    
    def generate_options_signals(
        self,
        symbol: str,
        market_data: pd.DataFrame,
        options_data: Dict[str, Any],
        fundamental_data: Optional[Dict[str, Any]] = None,
        sentiment_data: Optional[Dict[str, Any]] = None
    ) -> List[TradingSignal]:
        """
        Generate comprehensive options trading signals.
        
        Args:
            symbol: Stock symbol
            market_data: Historical price data
            options_data: Options chain and Greeks data
            fundamental_data: Fundamental analysis data
            sentiment_data: Market sentiment data
        
        Returns:
            List of trading signals
        """
        try:
            signals = []
            
            # Determine market regime
            market_regime = self._analyze_market_regime(market_data)
            
            # Generate different types of signals
            technical_signals = self._generate_technical_signals(symbol, market_data, market_regime)
            volatility_signals = self._generate_volatility_signals(symbol, market_data, options_data)
            momentum_signals = self._generate_momentum_signals(symbol, market_data)
            mean_reversion_signals = self._generate_mean_reversion_signals(symbol, market_data)
            
            # Combine all signals
            all_signals = technical_signals + volatility_signals + momentum_signals + mean_reversion_signals
            
            # Filter and rank signals
            filtered_signals = self._filter_signals(all_signals, market_regime)
            ranked_signals = self._rank_signals(filtered_signals)
            
            return ranked_signals[:10]  # Return top 10 signals
            
        except Exception as e:
            self.logger.error(f"Signal generation failed for {symbol}: {e}")
            return []
    
    def _analyze_market_regime(self, market_data: pd.DataFrame) -> MarketRegime:
        """Analyze current market regime."""
        try:
            if len(market_data) < 50:
                return MarketRegime("unknown", "medium", "sideways", 0.5, 0.5)
            
            # Calculate volatility
            returns = market_data['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(TRADING_DAYS_PER_YEAR)
            
            # Determine volatility level
            if volatility < 0.15:
                vol_level = "low"
            elif volatility < 0.30:
                vol_level = "medium"
            else:
                vol_level = "high"
            
            # Analyze trend
            sma_20 = self.technical_indicators.sma(market_data['close'], 20)
            sma_50 = self.technical_indicators.sma(market_data['close'], 50)
            
            current_price = market_data['close'].iloc[-1]
            sma_20_current = sma_20[-1] if len(sma_20) > 0 else current_price
            sma_50_current = sma_50[-1] if len(sma_50) > 0 else current_price
            
            # Determine trend direction
            if current_price > sma_20_current > sma_50_current:
                trend_direction = "up"
                trend_strength = min(1.0, (current_price - sma_50_current) / sma_50_current * 10)
            elif current_price < sma_20_current < sma_50_current:
                trend_direction = "down"
                trend_strength = min(1.0, (sma_50_current - current_price) / sma_50_current * 10)
            else:
                trend_direction = "sideways"
                trend_strength = 0.5
            
            # Determine regime type
            if abs(trend_strength) > 0.7:
                regime_type = "trending"
            elif volatility > 0.25:
                regime_type = "volatile"
            elif volatility < 0.15:
                regime_type = "calm"
            else:
                regime_type = "ranging"
            
            return MarketRegime(
                regime_type=regime_type,
                volatility_level=vol_level,
                trend_direction=trend_direction,
                strength=trend_strength,
                confidence=0.8
            )
            
        except Exception as e:
            self.logger.error(f"Market regime analysis failed: {e}")
            return MarketRegime("unknown", "medium", "sideways", 0.5, 0.5)
    
    def _generate_technical_signals(
        self, 
        symbol: str, 
        market_data: pd.DataFrame,
        market_regime: MarketRegime
    ) -> List[TradingSignal]:
        """Generate signals based on technical analysis."""
        signals = []
        
        try:
            if len(market_data) < 20:
                return signals
            
            current_price = market_data['close'].iloc[-1]
            
            # RSI signals
            rsi = self.technical_indicators.rsi(market_data['close'], 14)
            if len(rsi) > 0:
                current_rsi = rsi[-1]
                
                if current_rsi < 30:  # Oversold
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="rsi_oversold",
                        direction=SignalDirection.BULLISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.75,
                        entry_price=current_price,
                        metadata={"rsi": current_rsi, "regime": market_regime.regime_type}
                    ))
                elif current_rsi > 70:  # Overbought
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="rsi_overbought",
                        direction=SignalDirection.BEARISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.75,
                        entry_price=current_price,
                        metadata={"rsi": current_rsi, "regime": market_regime.regime_type}
                    ))
            
            # Moving average crossover signals
            sma_20 = self.technical_indicators.sma(market_data['close'], 20)
            sma_50 = self.technical_indicators.sma(market_data['close'], 50)
            
            if len(sma_20) >= 2 and len(sma_50) >= 2:
                # Golden cross
                if sma_20[-1] > sma_50[-1] and sma_20[-2] <= sma_50[-2]:
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="golden_cross",
                        direction=SignalDirection.BULLISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.80,
                        entry_price=current_price,
                        metadata={"sma_20": sma_20[-1], "sma_50": sma_50[-1]}
                    ))
                
                # Death cross
                elif sma_20[-1] < sma_50[-1] and sma_20[-2] >= sma_50[-2]:
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="death_cross",
                        direction=SignalDirection.BEARISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.80,
                        entry_price=current_price,
                        metadata={"sma_20": sma_20[-1], "sma_50": sma_50[-1]}
                    ))
            
            # Bollinger Bands signals
            bb_upper, bb_middle, bb_lower = self.technical_indicators.bollinger_bands(
                market_data['close'], 20, 2
            )
            
            if len(bb_upper) > 0:
                if current_price <= bb_lower[-1]:  # Price at lower band
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="bollinger_oversold",
                        direction=SignalDirection.BULLISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.70,
                        entry_price=current_price,
                        target_price=bb_middle[-1],
                        metadata={"bb_position": "lower_band"}
                    ))
                elif current_price >= bb_upper[-1]:  # Price at upper band
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="bollinger_overbought",
                        direction=SignalDirection.BEARISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.70,
                        entry_price=current_price,
                        target_price=bb_middle[-1],
                        metadata={"bb_position": "upper_band"}
                    ))
            
        except Exception as e:
            self.logger.error(f"Technical signal generation failed: {e}")

        return signals

    def _generate_volatility_signals(
        self,
        symbol: str,
        market_data: pd.DataFrame,
        options_data: Dict[str, Any]
    ) -> List[TradingSignal]:
        """Generate signals based on volatility analysis."""
        signals = []

        try:
            current_price = market_data['close'].iloc[-1]

            # Implied vs Historical volatility
            implied_vol = options_data.get('implied_volatility', 0)
            historical_vol = self._calculate_historical_volatility(market_data, 30)

            if implied_vol > 0 and historical_vol > 0:
                vol_ratio = implied_vol / historical_vol

                if vol_ratio > 1.2:  # IV significantly higher than HV
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="high_iv_rank",
                        direction=SignalDirection.BEARISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.75,
                        entry_price=current_price,
                        metadata={"iv": implied_vol, "hv": historical_vol, "ratio": vol_ratio}
                    ))
                elif vol_ratio < 0.8:  # IV significantly lower than HV
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="low_iv_rank",
                        direction=SignalDirection.BULLISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.75,
                        entry_price=current_price,
                        metadata={"iv": implied_vol, "hv": historical_vol, "ratio": vol_ratio}
                    ))

        except Exception as e:
            self.logger.error(f"Volatility signal generation failed: {e}")

        return signals

    def _generate_momentum_signals(self, symbol: str, market_data: pd.DataFrame) -> List[TradingSignal]:
        """Generate momentum-based signals."""
        signals = []

        try:
            if len(market_data) < 20:
                return signals

            current_price = market_data['close'].iloc[-1]

            # Price momentum
            returns_5d = (current_price / market_data['close'].iloc[-6] - 1) if len(market_data) >= 6 else 0
            returns_20d = (current_price / market_data['close'].iloc[-21] - 1) if len(market_data) >= 21 else 0

            if returns_5d > 0.05 and returns_20d > 0.10:  # Strong upward momentum
                signals.append(self._create_signal(
                    symbol=symbol,
                    signal_type="momentum_bullish",
                    direction=SignalDirection.BULLISH,
                    strength=SignalStrength.STRONG,
                    confidence=0.70,
                    entry_price=current_price,
                    metadata={"returns_5d": returns_5d, "returns_20d": returns_20d}
                ))
            elif returns_5d < -0.05 and returns_20d < -0.10:  # Strong downward momentum
                signals.append(self._create_signal(
                    symbol=symbol,
                    signal_type="momentum_bearish",
                    direction=SignalDirection.BEARISH,
                    strength=SignalStrength.STRONG,
                    confidence=0.70,
                    entry_price=current_price,
                    metadata={"returns_5d": returns_5d, "returns_20d": returns_20d}
                ))

        except Exception as e:
            self.logger.error(f"Momentum signal generation failed: {e}")

        return signals

    def _generate_mean_reversion_signals(self, symbol: str, market_data: pd.DataFrame) -> List[TradingSignal]:
        """Generate mean reversion signals."""
        signals = []

        try:
            if len(market_data) < 50:
                return signals

            current_price = market_data['close'].iloc[-1]

            # Z-score based mean reversion
            sma_20 = self.technical_indicators.sma(market_data['close'], 20)
            std_20 = market_data['close'].rolling(20).std()

            if len(sma_20) > 0 and len(std_20) > 0:
                z_score = (current_price - sma_20[-1]) / std_20.iloc[-1]

                if z_score < -2:  # Significantly below mean
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="mean_reversion_bullish",
                        direction=SignalDirection.BULLISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.65,
                        entry_price=current_price,
                        target_price=sma_20[-1],
                        metadata={"z_score": z_score}
                    ))
                elif z_score > 2:  # Significantly above mean
                    signals.append(self._create_signal(
                        symbol=symbol,
                        signal_type="mean_reversion_bearish",
                        direction=SignalDirection.BEARISH,
                        strength=SignalStrength.STRONG,
                        confidence=0.65,
                        entry_price=current_price,
                        target_price=sma_20[-1],
                        metadata={"z_score": z_score}
                    ))

        except Exception as e:
            self.logger.error(f"Mean reversion signal generation failed: {e}")

        return signals

    def _filter_signals(self, signals: List[TradingSignal], market_regime: MarketRegime) -> List[TradingSignal]:
        """Filter signals based on market regime and quality criteria."""
        filtered_signals = []

        for signal in signals:
            # Filter by confidence threshold
            if signal.confidence < 0.6:
                continue

            # Filter by market regime compatibility
            if market_regime.regime_type == "trending":
                # Prefer momentum signals in trending markets
                if signal.signal_type.startswith("momentum"):
                    signal.confidence *= 1.2
                elif signal.signal_type.startswith("mean_reversion"):
                    signal.confidence *= 0.8
            elif market_regime.regime_type == "ranging":
                # Prefer mean reversion signals in ranging markets
                if signal.signal_type.startswith("mean_reversion"):
                    signal.confidence *= 1.2
                elif signal.signal_type.startswith("momentum"):
                    signal.confidence *= 0.8

            filtered_signals.append(signal)

        return filtered_signals

    def _rank_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """Rank signals by quality score."""
        def signal_score(signal):
            base_score = signal.confidence * 100

            # Bonus for high probability of profit
            if signal.probability_of_profit > 0.7:
                base_score += 20
            elif signal.probability_of_profit > 0.6:
                base_score += 10

            # Bonus for good risk/reward ratio
            if signal.risk_reward_ratio > 2.0:
                base_score += 15
            elif signal.risk_reward_ratio > 1.5:
                base_score += 10

            return base_score

        return sorted(signals, key=signal_score, reverse=True)

    def _create_signal(
        self,
        symbol: str,
        signal_type: str,
        direction: SignalDirection,
        strength: SignalStrength,
        confidence: float,
        entry_price: float,
        target_price: Optional[float] = None,
        stop_loss: Optional[float] = None,
        time_horizon: int = 30,
        metadata: Optional[Dict[str, Any]] = None
    ) -> TradingSignal:
        """Create a trading signal with calculated metrics."""

        # Calculate probability of profit (simplified)
        prob_profit = min(0.9, confidence * 0.8 + 0.1)

        # Calculate expected return (simplified)
        if target_price:
            expected_return = abs(target_price - entry_price) / entry_price
        else:
            expected_return = 0.05  # Default 5% expected return

        # Calculate risk/reward ratio
        if stop_loss and target_price:
            risk = abs(entry_price - stop_loss)
            reward = abs(target_price - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 1.0
        else:
            risk_reward_ratio = 1.5  # Default ratio

        return TradingSignal(
            symbol=symbol,
            signal_type=signal_type,
            direction=direction,
            strength=strength,
            confidence=confidence,
            entry_price=entry_price,
            target_price=target_price,
            stop_loss=stop_loss,
            probability_of_profit=prob_profit,
            expected_return=expected_return,
            risk_reward_ratio=risk_reward_ratio,
            time_horizon=time_horizon,
            generated_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(days=time_horizon),
            metadata=metadata or {}
        )

    def _calculate_historical_volatility(self, market_data: pd.DataFrame, window: int) -> float:
        """Calculate historical volatility."""
        try:
            if len(market_data) < window:
                return 0.0

            returns = market_data['close'].pct_change().dropna()
            if len(returns) < window:
                return 0.0

            return returns.tail(window).std() * np.sqrt(TRADING_DAYS_PER_YEAR)
        except:
            return 0.0


# Global signal generator instance
signal_generator = SignalGenerator()
