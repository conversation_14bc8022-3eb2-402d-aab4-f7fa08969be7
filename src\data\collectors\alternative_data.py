"""
Alternative Data Collector for QuantEdgeFlow
Collection of alternative data sources including sentiment, news, and social media
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date, timedelta
import aiohttp
import json
import re
from textblob import TextBlob
import pandas as pd

from src.config.settings import get_settings
from src.config.database import get_async_session
from src.utils.logger import get_performance_logger

settings = get_settings()
logger = logging.getLogger(__name__)
performance_logger = get_performance_logger()


class AlternativeDataCollector:
    """Comprehensive alternative data collector."""
    
    def __init__(self):
        self.logger = logger
        self.session: Optional[aiohttp.ClientSession] = None
        
        # News sources and APIs
        self.news_sources = {
            'reddit': 'https://www.reddit.com/r/stocks/search.json',
            'newsapi': 'https://newsapi.org/v2/everything',
            'alpha_vantage_news': 'https://www.alphavantage.co/query'
        }
        
        # Sentiment analysis cache
        self.sentiment_cache = {}
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def collect_news_sentiment(self, symbol: str, days_back: int = 7) -> Dict[str, Any]:
        """Collect and analyze news sentiment for a symbol."""
        try:
            start_time = datetime.utcnow()
            
            # Collect news from multiple sources
            news_data = await self._collect_news_articles(symbol, days_back)
            
            # Analyze sentiment
            sentiment_analysis = self._analyze_news_sentiment(news_data)
            
            # Calculate sentiment metrics
            sentiment_metrics = self._calculate_sentiment_metrics(sentiment_analysis)
            
            result = {
                'symbol': symbol,
                'collection_date': datetime.utcnow().isoformat(),
                'period_days': days_back,
                'total_articles': len(news_data),
                'sentiment_metrics': sentiment_metrics,
                'articles': news_data[:10],  # Return top 10 articles
                'sentiment_analysis': sentiment_analysis
            }
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.log_api_performance(
                f"news_sentiment_{symbol}", duration, 200
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to collect news sentiment for {symbol}: {e}")
            return {}
    
    async def collect_social_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Collect social media sentiment data."""
        try:
            start_time = datetime.utcnow()
            
            # Collect from Reddit
            reddit_data = await self._collect_reddit_sentiment(symbol)
            
            # Collect from Twitter (if API available)
            twitter_data = await self._collect_twitter_sentiment(symbol)
            
            # Combine social sentiment
            combined_sentiment = self._combine_social_sentiment(reddit_data, twitter_data)
            
            result = {
                'symbol': symbol,
                'collection_date': datetime.utcnow().isoformat(),
                'reddit_sentiment': reddit_data,
                'twitter_sentiment': twitter_data,
                'combined_sentiment': combined_sentiment
            }
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.log_api_performance(
                f"social_sentiment_{symbol}", duration, 200
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to collect social sentiment for {symbol}: {e}")
            return {}
    
    async def collect_economic_indicators(self) -> Dict[str, Any]:
        """Collect relevant economic indicators."""
        try:
            start_time = datetime.utcnow()
            
            indicators = {}
            
            # VIX (Volatility Index)
            vix_data = await self._get_vix_data()
            if vix_data:
                indicators['vix'] = vix_data
            
            # Treasury rates
            treasury_data = await self._get_treasury_rates()
            if treasury_data:
                indicators['treasury_rates'] = treasury_data
            
            # Dollar index
            dxy_data = await self._get_dollar_index()
            if dxy_data:
                indicators['dollar_index'] = dxy_data
            
            # Crypto fear & greed index
            crypto_sentiment = await self._get_crypto_sentiment()
            if crypto_sentiment:
                indicators['crypto_sentiment'] = crypto_sentiment
            
            result = {
                'collection_date': datetime.utcnow().isoformat(),
                'indicators': indicators
            }
            
            # Log performance
            duration = (datetime.utcnow() - start_time).total_seconds()
            performance_logger.log_api_performance(
                "economic_indicators", duration, 200
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to collect economic indicators: {e}")
            return {}
    
    async def _collect_news_articles(self, symbol: str, days_back: int) -> List[Dict[str, Any]]:
        """Collect news articles from various sources."""
        articles = []
        
        try:
            # Alpha Vantage News (if API key available)
            if settings.ALPHA_VANTAGE_API_KEY:
                av_articles = await self._get_alpha_vantage_news(symbol)
                articles.extend(av_articles)
            
            # Add more news sources here
            # Note: Many news APIs require paid subscriptions
            
        except Exception as e:
            self.logger.error(f"Failed to collect news articles: {e}")
        
        return articles
    
    async def _get_alpha_vantage_news(self, symbol: str) -> List[Dict[str, Any]]:
        """Get news from Alpha Vantage."""
        try:
            if not self.session:
                return []
            
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'NEWS_SENTIMENT',
                'tickers': symbol,
                'apikey': settings.ALPHA_VANTAGE_API_KEY,
                'limit': 50
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    articles = []
                    if 'feed' in data:
                        for item in data['feed']:
                            articles.append({
                                'title': item.get('title', ''),
                                'summary': item.get('summary', ''),
                                'url': item.get('url', ''),
                                'time_published': item.get('time_published', ''),
                                'source': item.get('source', ''),
                                'sentiment_score': item.get('overall_sentiment_score', 0),
                                'sentiment_label': item.get('overall_sentiment_label', 'neutral')
                            })
                    
                    return articles
                
        except Exception as e:
            self.logger.error(f"Failed to get Alpha Vantage news: {e}")
        
        return []
    
    async def _collect_reddit_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Collect sentiment from Reddit."""
        try:
            if not self.session:
                return {}
            
            # Search Reddit for mentions of the symbol
            url = f"https://www.reddit.com/r/stocks/search.json"
            params = {
                'q': symbol,
                'sort': 'new',
                'limit': 25,
                't': 'week'
            }
            
            headers = {'User-Agent': 'QuantEdgeFlow/1.0'}
            
            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    posts = []
                    if 'data' in data and 'children' in data['data']:
                        for post in data['data']['children']:
                            post_data = post['data']
                            posts.append({
                                'title': post_data.get('title', ''),
                                'selftext': post_data.get('selftext', ''),
                                'score': post_data.get('score', 0),
                                'num_comments': post_data.get('num_comments', 0),
                                'created_utc': post_data.get('created_utc', 0),
                                'url': post_data.get('url', '')
                            })
                    
                    # Analyze sentiment of posts
                    sentiment_scores = []
                    for post in posts:
                        text = f"{post['title']} {post['selftext']}"
                        sentiment = self._analyze_text_sentiment(text)
                        sentiment_scores.append(sentiment)
                    
                    avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
                    
                    return {
                        'total_posts': len(posts),
                        'average_sentiment': avg_sentiment,
                        'sentiment_distribution': self._get_sentiment_distribution(sentiment_scores),
                        'posts': posts[:5]  # Return top 5 posts
                    }
                
        except Exception as e:
            self.logger.error(f"Failed to collect Reddit sentiment: {e}")
        
        return {}
    
    async def _collect_twitter_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Collect sentiment from Twitter (placeholder - requires Twitter API)."""
        # Note: Twitter API requires authentication and has rate limits
        # This is a placeholder implementation
        return {
            'total_tweets': 0,
            'average_sentiment': 0.0,
            'sentiment_distribution': {'positive': 0, 'neutral': 0, 'negative': 0},
            'note': 'Twitter API integration required'
        }
    
    def _analyze_news_sentiment(self, articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze sentiment of news articles."""
        if not articles:
            return {}
        
        sentiments = []
        for article in articles:
            text = f"{article.get('title', '')} {article.get('summary', '')}"
            sentiment = self._analyze_text_sentiment(text)
            sentiments.append(sentiment)
        
        return {
            'individual_sentiments': sentiments,
            'average_sentiment': sum(sentiments) / len(sentiments),
            'sentiment_distribution': self._get_sentiment_distribution(sentiments)
        }
    
    def _analyze_text_sentiment(self, text: str) -> float:
        """Analyze sentiment of text using TextBlob."""
        try:
            if not text.strip():
                return 0.0
            
            # Use TextBlob for sentiment analysis
            blob = TextBlob(text)
            return blob.sentiment.polarity  # Returns value between -1 and 1
            
        except Exception as e:
            self.logger.error(f"Failed to analyze text sentiment: {e}")
            return 0.0
    
    def _get_sentiment_distribution(self, sentiments: List[float]) -> Dict[str, int]:
        """Get distribution of sentiment scores."""
        positive = sum(1 for s in sentiments if s > 0.1)
        negative = sum(1 for s in sentiments if s < -0.1)
        neutral = len(sentiments) - positive - negative
        
        return {
            'positive': positive,
            'neutral': neutral,
            'negative': negative
        }
    
    def _calculate_sentiment_metrics(self, sentiment_analysis: Dict[str, Any]) -> Dict[str, float]:
        """Calculate comprehensive sentiment metrics."""
        if not sentiment_analysis:
            return {}
        
        avg_sentiment = sentiment_analysis.get('average_sentiment', 0)
        distribution = sentiment_analysis.get('sentiment_distribution', {})
        
        total = sum(distribution.values())
        if total == 0:
            return {}
        
        return {
            'average_sentiment': avg_sentiment,
            'positive_ratio': distribution.get('positive', 0) / total,
            'negative_ratio': distribution.get('negative', 0) / total,
            'neutral_ratio': distribution.get('neutral', 0) / total,
            'sentiment_strength': abs(avg_sentiment),
            'sentiment_direction': 'bullish' if avg_sentiment > 0.1 else 'bearish' if avg_sentiment < -0.1 else 'neutral'
        }
    
    def _combine_social_sentiment(self, reddit_data: Dict[str, Any], twitter_data: Dict[str, Any]) -> Dict[str, Any]:
        """Combine sentiment from multiple social sources."""
        reddit_sentiment = reddit_data.get('average_sentiment', 0)
        twitter_sentiment = twitter_data.get('average_sentiment', 0)
        
        # Weight Reddit more heavily as it's more reliable
        combined_sentiment = (reddit_sentiment * 0.7 + twitter_sentiment * 0.3)
        
        return {
            'combined_average_sentiment': combined_sentiment,
            'reddit_weight': 0.7,
            'twitter_weight': 0.3,
            'sentiment_direction': 'bullish' if combined_sentiment > 0.1 else 'bearish' if combined_sentiment < -0.1 else 'neutral'
        }
    
    async def _get_vix_data(self) -> Dict[str, Any]:
        """Get VIX volatility index data."""
        # Placeholder - would integrate with financial data provider
        return {'value': 20.0, 'change': -0.5, 'note': 'VIX integration required'}
    
    async def _get_treasury_rates(self) -> Dict[str, Any]:
        """Get treasury rates data."""
        # Placeholder - would integrate with treasury data provider
        return {'10_year': 4.5, '2_year': 4.8, 'note': 'Treasury API integration required'}
    
    async def _get_dollar_index(self) -> Dict[str, Any]:
        """Get dollar index data."""
        # Placeholder - would integrate with currency data provider
        return {'value': 103.5, 'change': 0.2, 'note': 'DXY integration required'}
    
    async def _get_crypto_sentiment(self) -> Dict[str, Any]:
        """Get crypto fear & greed index."""
        # Placeholder - would integrate with crypto sentiment provider
        return {'fear_greed_index': 45, 'classification': 'neutral', 'note': 'Crypto sentiment API required'}


# Global alternative data collector instance
alternative_data_collector = AlternativeDataCollector()
