"""
Trade Data Models for QuantEdgeFlow
Comprehensive trade tracking and execution management
"""

from datetime import datetime, date
from decimal import Decimal
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid

from sqlalchemy import (
    Column, String, Float, Integer, DateTime, Boolean, 
    Text, ForeignKey, Numeric, Date, JSON
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, validates
from pydantic import BaseModel, Field, validator
from pydantic.types import condecimal

from config.database import Base


class TradeStatus(str, Enum):
    """Trade status enumeration."""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class TradeType(str, Enum):
    """Trade type enumeration."""
    BUY = "buy"
    SELL = "sell"
    BUY_TO_OPEN = "buy_to_open"
    SELL_TO_OPEN = "sell_to_open"
    BUY_TO_CLOSE = "buy_to_close"
    SELL_TO_CLOSE = "sell_to_close"


class OrderType(str, Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"


class TimeInForce(str, Enum):
    """Time in force enumeration."""
    DAY = "day"
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate or Cancel
    FOK = "fok"  # Fill or Kill


class TradeStrategy(str, Enum):
    """Trading strategy enumeration."""
    COVERED_CALL = "covered_call"
    CASH_SECURED_PUT = "cash_secured_put"
    IRON_CONDOR = "iron_condor"
    BUTTERFLY = "butterfly"
    STRADDLE = "straddle"
    STRANGLE = "strangle"
    COLLAR = "collar"
    SPREAD = "spread"
    SINGLE_OPTION = "single_option"
    STOCK = "stock"


class Trade(Base):
    """Trade database model."""
    
    __tablename__ = "trades"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    portfolio_id = Column(UUID(as_uuid=True), ForeignKey("portfolios.id"), nullable=False)
    user_id = Column(String(255), nullable=False, index=True)
    
    # Trade identification
    external_order_id = Column(String(255))  # Broker order ID
    strategy_name = Column(String(100))
    trade_strategy = Column(String(50))
    
    # Instrument details
    symbol = Column(String(20), nullable=False, index=True)
    instrument_type = Column(String(20), nullable=False)  # stock, option, future
    
    # Order details
    trade_type = Column(String(20), nullable=False)
    order_type = Column(String(20), nullable=False)
    time_in_force = Column(String(10), default=TimeInForce.DAY)
    
    # Quantity and pricing
    quantity = Column(Integer, nullable=False)
    filled_quantity = Column(Integer, default=0)
    price = Column(Numeric(10, 4))  # Limit price
    filled_price = Column(Numeric(10, 4))  # Average fill price
    stop_price = Column(Numeric(10, 4))  # Stop price for stop orders
    
    # Options-specific fields
    option_type = Column(String(10))  # call, put
    strike_price = Column(Numeric(10, 4))
    expiration_date = Column(Date)
    
    # Financial details
    commission = Column(Numeric(10, 2), default=0)
    fees = Column(Numeric(10, 2), default=0)
    total_cost = Column(Numeric(15, 2))  # Total cost including fees
    
    # P&L tracking
    realized_pnl = Column(Numeric(15, 2), default=0)
    unrealized_pnl = Column(Numeric(15, 2), default=0)
    
    # Risk metrics at trade time
    delta = Column(Float, default=0.0)
    gamma = Column(Float, default=0.0)
    theta = Column(Float, default=0.0)
    vega = Column(Float, default=0.0)
    rho = Column(Float, default=0.0)
    implied_volatility = Column(Float)
    
    # Trade metadata
    status = Column(String(20), default=TradeStatus.PENDING)
    notes = Column(Text)
    tags = Column(JSON)  # Array of tags for categorization
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    submitted_at = Column(DateTime)
    filled_at = Column(DateTime)
    cancelled_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Risk management
    max_loss = Column(Numeric(15, 2))  # Maximum acceptable loss
    target_profit = Column(Numeric(15, 2))  # Target profit
    stop_loss_price = Column(Numeric(10, 4))
    take_profit_price = Column(Numeric(10, 4))
    
    # Relationships
    portfolio = relationship("Portfolio", back_populates="trades")
    legs = relationship("TradeLeg", back_populates="trade", cascade="all, delete-orphan")
    
    @validates('trade_type')
    def validate_trade_type(self, key, value):
        """Validate trade type."""
        if value not in [e.value for e in TradeType]:
            raise ValueError(f"Invalid trade type: {value}")
        return value
    
    @validates('order_type')
    def validate_order_type(self, key, value):
        """Validate order type."""
        if value not in [e.value for e in OrderType]:
            raise ValueError(f"Invalid order type: {value}")
        return value
    
    @validates('status')
    def validate_status(self, key, value):
        """Validate trade status."""
        if value not in [e.value for e in TradeStatus]:
            raise ValueError(f"Invalid trade status: {value}")
        return value
    
    def calculate_fill_percentage(self) -> float:
        """Calculate fill percentage."""
        if self.quantity == 0:
            return 0.0
        return (self.filled_quantity / abs(self.quantity)) * 100
    
    def is_complete(self) -> bool:
        """Check if trade is completely filled."""
        return self.filled_quantity == abs(self.quantity)
    
    def calculate_total_value(self) -> Decimal:
        """Calculate total trade value."""
        if not self.filled_price:
            return Decimal(0)
        return Decimal(self.filled_price) * Decimal(abs(self.filled_quantity))


class TradeLeg(Base):
    """Trade leg for multi-leg strategies."""
    
    __tablename__ = "trade_legs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    trade_id = Column(UUID(as_uuid=True), ForeignKey("trades.id"), nullable=False)
    
    # Leg details
    leg_number = Column(Integer, nullable=False)  # 1, 2, 3, 4 for multi-leg trades
    symbol = Column(String(20), nullable=False)
    instrument_type = Column(String(20), nullable=False)
    
    # Order details
    trade_type = Column(String(20), nullable=False)
    quantity = Column(Integer, nullable=False)
    price = Column(Numeric(10, 4))
    filled_price = Column(Numeric(10, 4))
    
    # Options-specific
    option_type = Column(String(10))
    strike_price = Column(Numeric(10, 4))
    expiration_date = Column(Date)
    
    # Status
    status = Column(String(20), default=TradeStatus.PENDING)
    filled_quantity = Column(Integer, default=0)
    
    # Greeks at execution
    delta = Column(Float, default=0.0)
    gamma = Column(Float, default=0.0)
    theta = Column(Float, default=0.0)
    vega = Column(Float, default=0.0)
    rho = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    filled_at = Column(DateTime)
    
    # Relationships
    trade = relationship("Trade", back_populates="legs")


# Pydantic models for API
class TradeBase(BaseModel):
    """Base trade model for API."""
    symbol: str = Field(..., min_length=1, max_length=20)
    instrument_type: str = Field(..., regex="^(stock|option|future|etf)$")
    trade_type: str = Field(..., regex="^(buy|sell|buy_to_open|sell_to_open|buy_to_close|sell_to_close)$")
    order_type: str = Field(..., regex="^(market|limit|stop|stop_limit|trailing_stop)$")
    quantity: int = Field(..., ne=0)
    price: Optional[condecimal(gt=0, decimal_places=4)] = None
    stop_price: Optional[condecimal(gt=0, decimal_places=4)] = None
    time_in_force: str = Field(default="day", regex="^(day|gtc|ioc|fok)$")
    
    # Options-specific fields
    option_type: Optional[str] = Field(None, regex="^(call|put)$")
    strike_price: Optional[condecimal(gt=0, decimal_places=4)] = None
    expiration_date: Optional[date] = None
    
    # Strategy and risk management
    trade_strategy: Optional[str] = None
    max_loss: Optional[condecimal(decimal_places=2)] = None
    target_profit: Optional[condecimal(decimal_places=2)] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = []
    
    @validator('price')
    def validate_price_for_limit_orders(cls, v, values):
        """Validate price is provided for limit orders."""
        if values.get('order_type') in ['limit', 'stop_limit'] and v is None:
            raise ValueError('Price is required for limit and stop-limit orders')
        return v
    
    @validator('stop_price')
    def validate_stop_price_for_stop_orders(cls, v, values):
        """Validate stop price is provided for stop orders."""
        if values.get('order_type') in ['stop', 'stop_limit', 'trailing_stop'] and v is None:
            raise ValueError('Stop price is required for stop orders')
        return v


class TradeCreate(TradeBase):
    """Trade creation model."""
    pass


class TradeLegBase(BaseModel):
    """Base trade leg model."""
    leg_number: int = Field(..., ge=1, le=4)
    symbol: str = Field(..., min_length=1, max_length=20)
    instrument_type: str = Field(..., regex="^(stock|option|future|etf)$")
    trade_type: str = Field(..., regex="^(buy|sell|buy_to_open|sell_to_open|buy_to_close|sell_to_close)$")
    quantity: int = Field(..., ne=0)
    price: Optional[condecimal(gt=0, decimal_places=4)] = None
    
    # Options-specific
    option_type: Optional[str] = Field(None, regex="^(call|put)$")
    strike_price: Optional[condecimal(gt=0, decimal_places=4)] = None
    expiration_date: Optional[date] = None


class MultiLegTradeCreate(BaseModel):
    """Multi-leg trade creation model."""
    strategy_name: str = Field(..., min_length=1, max_length=100)
    trade_strategy: str
    legs: List[TradeLegBase] = Field(..., min_items=2, max_items=4)
    order_type: str = Field(..., regex="^(market|limit|stop|stop_limit)$")
    time_in_force: str = Field(default="day", regex="^(day|gtc|ioc|fok)$")
    max_loss: Optional[condecimal(decimal_places=2)] = None
    target_profit: Optional[condecimal(decimal_places=2)] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = []


class TradeResponse(TradeBase):
    """Trade response model."""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    user_id: str
    external_order_id: Optional[str] = None
    strategy_name: Optional[str] = None
    status: str
    filled_quantity: int = 0
    filled_price: Optional[Decimal] = None
    commission: Decimal = Decimal(0)
    fees: Decimal = Decimal(0)
    total_cost: Optional[Decimal] = None
    realized_pnl: Decimal = Decimal(0)
    unrealized_pnl: Decimal = Decimal(0)
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0
    rho: float = 0.0
    implied_volatility: Optional[float] = None
    created_at: datetime
    submitted_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TradeLegResponse(TradeLegBase):
    """Trade leg response model."""
    id: uuid.UUID
    trade_id: uuid.UUID
    status: str
    filled_quantity: int = 0
    filled_price: Optional[Decimal] = None
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0
    rho: float = 0.0
    created_at: datetime
    filled_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
