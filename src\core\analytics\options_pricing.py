"""
Options Pricing Engine for QuantEdgeFlow
Advanced options pricing models with Greeks calculation and volatility analysis
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date
from dataclasses import dataclass
from scipy.stats import norm
from scipy.optimize import minimize_scalar
import warnings

from core.algorithms.black_scholes import BlackScholesCalculator, OptionParameters
from core.algorithms.monte_carlo import MonteCarloEngine
from core.algorithms.volatility_models import VolatilityCalculator
from utils.constants import TRADING_DAYS_PER_YEAR

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


@dataclass
class OptionPricingInputs:
    """Input parameters for options pricing."""
    underlying_price: float
    strike_price: float
    time_to_expiration: float  # In years
    risk_free_rate: float
    volatility: float
    option_type: str  # 'call' or 'put'
    dividend_yield: float = 0.0
    american_style: bool = False


@dataclass
class OptionPricingResult:
    """Result of options pricing calculation."""
    theoretical_price: float
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    implied_volatility: Optional[float] = None
    intrinsic_value: float = 0.0
    time_value: float = 0.0
    probability_itm: float = 0.0
    probability_profit: float = 0.0


class OptionsPricingEngine:
    """Comprehensive options pricing engine with multiple models."""
    
    def __init__(self):
        self.logger = logger
        self.bs_calculator = BlackScholesCalculator()
        self.mc_engine = MonteCarloEngine()
        self.vol_calculator = VolatilityCalculator()
    
    def calculate_option_price(
        self, 
        inputs: OptionPricingInputs,
        model: str = "black_scholes"
    ) -> OptionPricingResult:
        """
        Calculate option price using specified model.
        
        Args:
            inputs: Option pricing inputs
            model: Pricing model ('black_scholes', 'monte_carlo', 'binomial')
        
        Returns:
            OptionPricingResult with price and Greeks
        """
        try:
            if model == "black_scholes":
                return self._black_scholes_pricing(inputs)
            elif model == "monte_carlo":
                return self._monte_carlo_pricing(inputs)
            elif model == "binomial":
                return self._binomial_pricing(inputs)
            else:
                raise ValueError(f"Unsupported pricing model: {model}")
                
        except Exception as e:
            self.logger.error(f"Option pricing failed: {e}")
            return self._empty_pricing_result()
    
    def _black_scholes_pricing(self, inputs: OptionPricingInputs) -> OptionPricingResult:
        """Calculate option price using Black-Scholes model."""
        try:
            # Create option parameters
            option_params = OptionParameters(
                underlying_price=inputs.underlying_price,
                strike_price=inputs.strike_price,
                time_to_expiration=inputs.time_to_expiration,
                risk_free_rate=inputs.risk_free_rate,
                volatility=inputs.volatility,
                dividend_yield=inputs.dividend_yield
            )
            
            # Calculate price and Greeks
            if inputs.option_type.lower() == 'call':
                price = self.bs_calculator.call_price(option_params)
                delta = self.bs_calculator.call_delta(option_params)
            else:
                price = self.bs_calculator.put_price(option_params)
                delta = self.bs_calculator.put_delta(option_params)
            
            gamma = self.bs_calculator.gamma(option_params)
            theta = self.bs_calculator.theta(option_params, inputs.option_type.lower())
            vega = self.bs_calculator.vega(option_params)
            rho = self.bs_calculator.rho(option_params, inputs.option_type.lower())
            
            # Calculate additional metrics
            intrinsic_value = self._calculate_intrinsic_value(
                inputs.underlying_price, inputs.strike_price, inputs.option_type
            )
            time_value = max(0, price - intrinsic_value)
            
            prob_itm = self._calculate_probability_itm(inputs)
            prob_profit = self._calculate_probability_profit(inputs, price)
            
            return OptionPricingResult(
                theoretical_price=price,
                delta=delta,
                gamma=gamma,
                theta=theta,
                vega=vega,
                rho=rho,
                intrinsic_value=intrinsic_value,
                time_value=time_value,
                probability_itm=prob_itm,
                probability_profit=prob_profit
            )
            
        except Exception as e:
            self.logger.error(f"Black-Scholes pricing failed: {e}")
            return self._empty_pricing_result()
    
    def _monte_carlo_pricing(self, inputs: OptionPricingInputs) -> OptionPricingResult:
        """Calculate option price using Monte Carlo simulation."""
        try:
            # Use Monte Carlo engine for pricing
            mc_result = self.mc_engine.price_option(
                S0=inputs.underlying_price,
                K=inputs.strike_price,
                T=inputs.time_to_expiration,
                r=inputs.risk_free_rate,
                sigma=inputs.volatility,
                option_type=inputs.option_type,
                n_simulations=10000,
                n_steps=252
            )
            
            # Calculate Greeks using finite difference
            delta = self._calculate_delta_finite_diff(inputs)
            gamma = self._calculate_gamma_finite_diff(inputs)
            theta = self._calculate_theta_finite_diff(inputs)
            vega = self._calculate_vega_finite_diff(inputs)
            rho = self._calculate_rho_finite_diff(inputs)
            
            intrinsic_value = self._calculate_intrinsic_value(
                inputs.underlying_price, inputs.strike_price, inputs.option_type
            )
            time_value = max(0, mc_result['price'] - intrinsic_value)
            
            return OptionPricingResult(
                theoretical_price=mc_result['price'],
                delta=delta,
                gamma=gamma,
                theta=theta,
                vega=vega,
                rho=rho,
                intrinsic_value=intrinsic_value,
                time_value=time_value,
                probability_itm=mc_result.get('prob_itm', 0.0),
                probability_profit=mc_result.get('prob_profit', 0.0)
            )
            
        except Exception as e:
            self.logger.error(f"Monte Carlo pricing failed: {e}")
            return self._empty_pricing_result()
    
    def _binomial_pricing(self, inputs: OptionPricingInputs) -> OptionPricingResult:
        """Calculate option price using binomial tree model."""
        try:
            n_steps = min(100, max(10, int(inputs.time_to_expiration * 252)))
            
            dt = inputs.time_to_expiration / n_steps
            u = np.exp(inputs.volatility * np.sqrt(dt))
            d = 1 / u
            p = (np.exp(inputs.risk_free_rate * dt) - d) / (u - d)
            
            # Initialize asset prices at maturity
            asset_prices = np.zeros(n_steps + 1)
            for i in range(n_steps + 1):
                asset_prices[i] = inputs.underlying_price * (u ** (n_steps - i)) * (d ** i)
            
            # Calculate option values at maturity
            option_values = np.zeros(n_steps + 1)
            for i in range(n_steps + 1):
                if inputs.option_type.lower() == 'call':
                    option_values[i] = max(0, asset_prices[i] - inputs.strike_price)
                else:
                    option_values[i] = max(0, inputs.strike_price - asset_prices[i])
            
            # Backward induction
            for j in range(n_steps - 1, -1, -1):
                for i in range(j + 1):
                    option_values[i] = np.exp(-inputs.risk_free_rate * dt) * (
                        p * option_values[i] + (1 - p) * option_values[i + 1]
                    )
                    
                    if inputs.american_style:
                        # Early exercise for American options
                        asset_price = inputs.underlying_price * (u ** (j - i)) * (d ** i)
                        if inputs.option_type.lower() == 'call':
                            exercise_value = max(0, asset_price - inputs.strike_price)
                        else:
                            exercise_value = max(0, inputs.strike_price - asset_price)
                        option_values[i] = max(option_values[i], exercise_value)
            
            price = option_values[0]
            
            # Calculate Greeks using finite difference
            delta = self._calculate_delta_finite_diff(inputs)
            gamma = self._calculate_gamma_finite_diff(inputs)
            theta = self._calculate_theta_finite_diff(inputs)
            vega = self._calculate_vega_finite_diff(inputs)
            rho = self._calculate_rho_finite_diff(inputs)
            
            intrinsic_value = self._calculate_intrinsic_value(
                inputs.underlying_price, inputs.strike_price, inputs.option_type
            )
            time_value = max(0, price - intrinsic_value)
            
            return OptionPricingResult(
                theoretical_price=price,
                delta=delta,
                gamma=gamma,
                theta=theta,
                vega=vega,
                rho=rho,
                intrinsic_value=intrinsic_value,
                time_value=time_value,
                probability_itm=self._calculate_probability_itm(inputs),
                probability_profit=self._calculate_probability_profit(inputs, price)
            )
            
        except Exception as e:
            self.logger.error(f"Binomial pricing failed: {e}")
            return self._empty_pricing_result()
    
    def calculate_implied_volatility(
        self, 
        market_price: float,
        inputs: OptionPricingInputs,
        max_iterations: int = 100,
        tolerance: float = 1e-6
    ) -> float:
        """Calculate implied volatility using Newton-Raphson method."""
        try:
            def objective(vol):
                inputs_copy = OptionPricingInputs(
                    underlying_price=inputs.underlying_price,
                    strike_price=inputs.strike_price,
                    time_to_expiration=inputs.time_to_expiration,
                    risk_free_rate=inputs.risk_free_rate,
                    volatility=vol,
                    option_type=inputs.option_type,
                    dividend_yield=inputs.dividend_yield
                )
                theoretical_price = self._black_scholes_pricing(inputs_copy).theoretical_price
                return (theoretical_price - market_price) ** 2
            
            result = minimize_scalar(
                objective,
                bounds=(0.01, 5.0),
                method='bounded'
            )
            
            return result.x if result.success else 0.0
            
        except Exception as e:
            self.logger.error(f"Implied volatility calculation failed: {e}")
            return 0.0

    def _calculate_intrinsic_value(self, spot: float, strike: float, option_type: str) -> float:
        """Calculate intrinsic value of option."""
        if option_type.lower() == 'call':
            return max(0, spot - strike)
        else:
            return max(0, strike - spot)

    def _calculate_probability_itm(self, inputs: OptionPricingInputs) -> float:
        """Calculate probability of finishing in-the-money."""
        try:
            d2 = (np.log(inputs.underlying_price / inputs.strike_price) +
                  (inputs.risk_free_rate - 0.5 * inputs.volatility**2) * inputs.time_to_expiration) / \
                 (inputs.volatility * np.sqrt(inputs.time_to_expiration))

            if inputs.option_type.lower() == 'call':
                return norm.cdf(d2)
            else:
                return norm.cdf(-d2)
        except:
            return 0.0

    def _calculate_probability_profit(self, inputs: OptionPricingInputs, option_price: float) -> float:
        """Calculate probability of profit at expiration."""
        try:
            if inputs.option_type.lower() == 'call':
                breakeven = inputs.strike_price + option_price
                d = (np.log(inputs.underlying_price / breakeven) +
                     (inputs.risk_free_rate - 0.5 * inputs.volatility**2) * inputs.time_to_expiration) / \
                    (inputs.volatility * np.sqrt(inputs.time_to_expiration))
                return norm.cdf(d)
            else:
                breakeven = inputs.strike_price - option_price
                d = (np.log(inputs.underlying_price / breakeven) +
                     (inputs.risk_free_rate - 0.5 * inputs.volatility**2) * inputs.time_to_expiration) / \
                    (inputs.volatility * np.sqrt(inputs.time_to_expiration))
                return norm.cdf(-d)
        except:
            return 0.0

    def _calculate_delta_finite_diff(self, inputs: OptionPricingInputs) -> float:
        """Calculate delta using finite difference method."""
        try:
            h = 0.01 * inputs.underlying_price
            inputs_up = OptionPricingInputs(**{**inputs.__dict__, 'underlying_price': inputs.underlying_price + h})
            inputs_down = OptionPricingInputs(**{**inputs.__dict__, 'underlying_price': inputs.underlying_price - h})

            price_up = self._black_scholes_pricing(inputs_up).theoretical_price
            price_down = self._black_scholes_pricing(inputs_down).theoretical_price

            return (price_up - price_down) / (2 * h)
        except:
            return 0.0

    def _calculate_gamma_finite_diff(self, inputs: OptionPricingInputs) -> float:
        """Calculate gamma using finite difference method."""
        try:
            h = 0.01 * inputs.underlying_price
            inputs_up = OptionPricingInputs(**{**inputs.__dict__, 'underlying_price': inputs.underlying_price + h})
            inputs_down = OptionPricingInputs(**{**inputs.__dict__, 'underlying_price': inputs.underlying_price - h})

            delta_up = self._calculate_delta_finite_diff(inputs_up)
            delta_down = self._calculate_delta_finite_diff(inputs_down)

            return (delta_up - delta_down) / (2 * h)
        except:
            return 0.0

    def _calculate_theta_finite_diff(self, inputs: OptionPricingInputs) -> float:
        """Calculate theta using finite difference method."""
        try:
            h = 1.0 / 365.0  # One day
            if inputs.time_to_expiration <= h:
                return 0.0

            inputs_future = OptionPricingInputs(**{**inputs.__dict__, 'time_to_expiration': inputs.time_to_expiration - h})

            price_current = self._black_scholes_pricing(inputs).theoretical_price
            price_future = self._black_scholes_pricing(inputs_future).theoretical_price

            return price_future - price_current
        except:
            return 0.0

    def _calculate_vega_finite_diff(self, inputs: OptionPricingInputs) -> float:
        """Calculate vega using finite difference method."""
        try:
            h = 0.01  # 1% volatility change
            inputs_up = OptionPricingInputs(**{**inputs.__dict__, 'volatility': inputs.volatility + h})
            inputs_down = OptionPricingInputs(**{**inputs.__dict__, 'volatility': inputs.volatility - h})

            price_up = self._black_scholes_pricing(inputs_up).theoretical_price
            price_down = self._black_scholes_pricing(inputs_down).theoretical_price

            return (price_up - price_down) / (2 * h)
        except:
            return 0.0

    def _calculate_rho_finite_diff(self, inputs: OptionPricingInputs) -> float:
        """Calculate rho using finite difference method."""
        try:
            h = 0.01  # 1% rate change
            inputs_up = OptionPricingInputs(**{**inputs.__dict__, 'risk_free_rate': inputs.risk_free_rate + h})
            inputs_down = OptionPricingInputs(**{**inputs.__dict__, 'risk_free_rate': inputs.risk_free_rate - h})

            price_up = self._black_scholes_pricing(inputs_up).theoretical_price
            price_down = self._black_scholes_pricing(inputs_down).theoretical_price

            return (price_up - price_down) / (2 * h)
        except:
            return 0.0

    def _empty_pricing_result(self) -> OptionPricingResult:
        """Return empty pricing result for error cases."""
        return OptionPricingResult(
            theoretical_price=0.0,
            delta=0.0,
            gamma=0.0,
            theta=0.0,
            vega=0.0,
            rho=0.0,
            intrinsic_value=0.0,
            time_value=0.0,
            probability_itm=0.0,
            probability_profit=0.0
        )


# Global options pricing engine instance
options_pricing_engine = OptionsPricingEngine()
