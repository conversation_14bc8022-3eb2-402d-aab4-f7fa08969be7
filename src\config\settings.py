"""
Configuration settings for QuantEdgeFlow application
Environment-based configuration using Pydantic Settings
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application Settings
    APP_NAME: str = "QuantEdgeFlow"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # Security Settings
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM")
    
    # CORS Settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="ALLOWED_ORIGINS"
    )
    
    # Database Settings
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    
    # Redis Settings
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_POOL_SIZE: int = Field(default=10, env="REDIS_POOL_SIZE")
    
    # Market Data API Settings
    ALPHA_VANTAGE_API_KEY: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    POLYGON_API_KEY: Optional[str] = Field(default=None, env="POLYGON_API_KEY")
    YAHOO_FINANCE_ENABLED: bool = Field(default=True, env="YAHOO_FINANCE_ENABLED")
    
    # Options Data Settings
    OPTIONS_DATA_REFRESH_INTERVAL: int = Field(default=60, env="OPTIONS_DATA_REFRESH_INTERVAL")  # seconds
    MAX_OPTION_CHAINS_PER_REQUEST: int = Field(default=50, env="MAX_OPTION_CHAINS_PER_REQUEST")
    
    # Trading Settings
    DEFAULT_NAV: float = Field(default=100000.0, env="DEFAULT_NAV")  # $100k default
    MAX_TRADES_PER_CYCLE: int = Field(default=5, env="MAX_TRADES_PER_CYCLE")
    MIN_PROBABILITY_OF_PROFIT: float = Field(default=0.65, env="MIN_PROBABILITY_OF_PROFIT")
    MIN_CREDIT_LOSS_RATIO: float = Field(default=0.33, env="MIN_CREDIT_LOSS_RATIO")
    MAX_LOSS_PERCENTAGE: float = Field(default=0.005, env="MAX_LOSS_PERCENTAGE")  # 0.5%
    
    # Portfolio Risk Limits
    MAX_PORTFOLIO_DELTA: float = Field(default=0.30, env="MAX_PORTFOLIO_DELTA")
    MIN_PORTFOLIO_VEGA: float = Field(default=-0.05, env="MIN_PORTFOLIO_VEGA")
    MAX_TRADES_PER_SECTOR: int = Field(default=2, env="MAX_TRADES_PER_SECTOR")
    
    # Analytics Settings
    VOLATILITY_LOOKBACK_DAYS: int = Field(default=252, env="VOLATILITY_LOOKBACK_DAYS")
    RISK_FREE_RATE: float = Field(default=0.05, env="RISK_FREE_RATE")  # 5% default
    
    # Logging Settings
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # Rate Limiting Settings
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Monitoring Settings
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    PROMETHEUS_ENABLED: bool = Field(default=False, env="PROMETHEUS_ENABLED")
    
    # Background Tasks Settings
    MARKET_DATA_COLLECTION_ENABLED: bool = Field(default=True, env="MARKET_DATA_COLLECTION_ENABLED")
    PORTFOLIO_REBALANCE_INTERVAL: int = Field(default=300, env="PORTFOLIO_REBALANCE_INTERVAL")  # 5 minutes
    
    # External Services
    NOTIFICATION_WEBHOOK_URL: Optional[str] = Field(default=None, env="NOTIFICATION_WEBHOOK_URL")
    SLACK_WEBHOOK_URL: Optional[str] = Field(default=None, env="SLACK_WEBHOOK_URL")
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("DATABASE_URL")
    def validate_database_url(cls, v):
        """Validate database URL format."""
        if not v.startswith(("postgresql://", "postgresql+asyncpg://")):
            raise ValueError("DATABASE_URL must be a valid PostgreSQL URL")
        return v
    
    @validator("MIN_PROBABILITY_OF_PROFIT")
    def validate_probability_range(cls, v):
        """Validate probability is between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("Probability of profit must be between 0 and 1")
        return v
    
    @validator("MAX_LOSS_PERCENTAGE")
    def validate_loss_percentage(cls, v):
        """Validate loss percentage is positive and reasonable."""
        if not 0 < v < 1:
            raise ValueError("Max loss percentage must be between 0 and 1")
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get application settings singleton."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reload_settings() -> Settings:
    """Reload settings from environment (useful for testing)."""
    global _settings
    _settings = Settings()
    return _settings
