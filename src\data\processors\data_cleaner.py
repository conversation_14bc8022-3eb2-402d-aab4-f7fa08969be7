"""
Data Cleaner for QuantEdgeFlow
Comprehensive data cleaning and preprocessing for market data
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, date, timedelta
from decimal import Decimal
import warnings

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


class DataCleaner:
    """Comprehensive data cleaning and preprocessing engine."""
    
    def __init__(self):
        self.logger = logger
        
        # Data quality thresholds
        self.quality_thresholds = {
            'max_missing_ratio': 0.3,  # Maximum 30% missing data
            'min_data_points': 10,     # Minimum data points required
            'outlier_std_threshold': 3, # Standard deviations for outlier detection
            'max_price_change': 0.5,   # Maximum 50% price change in one period
            'min_volume_ratio': 0.01   # Minimum volume ratio to average
        }
    
    def clean_market_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess market data."""
        try:
            if data.empty:
                return data
            
            self.logger.info(f"Cleaning market data with {len(data)} rows")
            
            # Make a copy to avoid modifying original data
            cleaned_data = data.copy()
            
            # Step 1: Handle missing values
            cleaned_data = self._handle_missing_values(cleaned_data)
            
            # Step 2: Remove duplicates
            cleaned_data = self._remove_duplicates(cleaned_data)
            
            # Step 3: Detect and handle outliers
            cleaned_data = self._handle_outliers(cleaned_data)
            
            # Step 4: Validate price relationships
            cleaned_data = self._validate_price_relationships(cleaned_data)
            
            # Step 5: Clean volume data
            cleaned_data = self._clean_volume_data(cleaned_data)
            
            # Step 6: Sort by timestamp
            if 'timestamp' in cleaned_data.columns:
                cleaned_data = cleaned_data.sort_values('timestamp')
            elif cleaned_data.index.name in ['date', 'timestamp']:
                cleaned_data = cleaned_data.sort_index()
            
            self.logger.info(f"Data cleaning completed. {len(cleaned_data)} rows remaining")
            
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"Data cleaning failed: {e}")
            return data
    
    def clean_options_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess options data."""
        try:
            if data.empty:
                return data
            
            self.logger.info(f"Cleaning options data with {len(data)} rows")
            
            cleaned_data = data.copy()
            
            # Step 1: Handle missing values
            cleaned_data = self._handle_missing_values(cleaned_data)
            
            # Step 2: Remove invalid options
            cleaned_data = self._remove_invalid_options(cleaned_data)
            
            # Step 3: Validate Greeks
            cleaned_data = self._validate_greeks(cleaned_data)
            
            # Step 4: Clean implied volatility
            cleaned_data = self._clean_implied_volatility(cleaned_data)
            
            # Step 5: Validate option prices
            cleaned_data = self._validate_option_prices(cleaned_data)
            
            self.logger.info(f"Options data cleaning completed. {len(cleaned_data)} rows remaining")
            
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"Options data cleaning failed: {e}")
            return data
    
    def clean_fundamental_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and preprocess fundamental data."""
        try:
            if not data:
                return data
            
            self.logger.info("Cleaning fundamental data")
            
            cleaned_data = data.copy()
            
            # Step 1: Convert string numbers to float
            cleaned_data = self._convert_numeric_fields(cleaned_data)
            
            # Step 2: Handle missing or invalid values
            cleaned_data = self._clean_fundamental_values(cleaned_data)
            
            # Step 3: Validate ratios and metrics
            cleaned_data = self._validate_fundamental_ratios(cleaned_data)
            
            self.logger.info("Fundamental data cleaning completed")
            
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"Fundamental data cleaning failed: {e}")
            return data
    
    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values in the dataset."""
        try:
            # Check missing value ratio
            missing_ratio = data.isnull().sum() / len(data)
            
            # Remove columns with too many missing values
            cols_to_drop = missing_ratio[missing_ratio > self.quality_thresholds['max_missing_ratio']].index
            if len(cols_to_drop) > 0:
                self.logger.warning(f"Dropping columns with high missing ratio: {list(cols_to_drop)}")
                data = data.drop(columns=cols_to_drop)
            
            # Forward fill for price data
            price_columns = ['open', 'high', 'low', 'close', 'adj_close']
            for col in price_columns:
                if col in data.columns:
                    data[col] = data[col].fillna(method='ffill')
            
            # Interpolate for volume data
            if 'volume' in data.columns:
                data['volume'] = data['volume'].interpolate()
            
            # Drop rows with remaining missing values in critical columns
            critical_columns = ['close']
            for col in critical_columns:
                if col in data.columns:
                    data = data.dropna(subset=[col])
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to handle missing values: {e}")
            return data
    
    def _remove_duplicates(self, data: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate rows."""
        try:
            initial_count = len(data)
            
            # Remove exact duplicates
            data = data.drop_duplicates()
            
            # Remove duplicates based on timestamp if available
            if 'timestamp' in data.columns:
                data = data.drop_duplicates(subset=['timestamp'], keep='last')
            elif data.index.name in ['date', 'timestamp']:
                data = data[~data.index.duplicated(keep='last')]
            
            removed_count = initial_count - len(data)
            if removed_count > 0:
                self.logger.info(f"Removed {removed_count} duplicate rows")
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to remove duplicates: {e}")
            return data
    
    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """Detect and handle outliers in the data."""
        try:
            # Calculate returns for outlier detection
            if 'close' in data.columns:
                returns = data['close'].pct_change().dropna()
                
                # Detect outliers using z-score
                z_scores = np.abs((returns - returns.mean()) / returns.std())
                outlier_mask = z_scores > self.quality_thresholds['outlier_std_threshold']
                
                if outlier_mask.any():
                    outlier_count = outlier_mask.sum()
                    self.logger.warning(f"Detected {outlier_count} outliers in returns")
                    
                    # Cap extreme outliers instead of removing them
                    outlier_indices = returns[outlier_mask].index
                    for idx in outlier_indices:
                        if idx in data.index:
                            # Replace with interpolated value
                            prev_idx = data.index.get_loc(idx) - 1
                            next_idx = data.index.get_loc(idx) + 1
                            
                            if prev_idx >= 0 and next_idx < len(data):
                                prev_price = data.iloc[prev_idx]['close']
                                next_price = data.iloc[next_idx]['close']
                                data.loc[idx, 'close'] = (prev_price + next_price) / 2
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to handle outliers: {e}")
            return data
    
    def _validate_price_relationships(self, data: pd.DataFrame) -> pd.DataFrame:
        """Validate OHLC price relationships."""
        try:
            price_columns = ['open', 'high', 'low', 'close']
            
            if all(col in data.columns for col in price_columns):
                # Check if high >= max(open, close) and low <= min(open, close)
                invalid_high = data['high'] < np.maximum(data['open'], data['close'])
                invalid_low = data['low'] > np.minimum(data['open'], data['close'])
                
                invalid_rows = invalid_high | invalid_low
                
                if invalid_rows.any():
                    invalid_count = invalid_rows.sum()
                    self.logger.warning(f"Found {invalid_count} rows with invalid OHLC relationships")
                    
                    # Fix invalid relationships
                    for idx in data[invalid_rows].index:
                        row = data.loc[idx]
                        
                        # Ensure high is the maximum
                        data.loc[idx, 'high'] = max(row['open'], row['high'], row['low'], row['close'])
                        
                        # Ensure low is the minimum
                        data.loc[idx, 'low'] = min(row['open'], row['high'], row['low'], row['close'])
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to validate price relationships: {e}")
            return data
    
    def _clean_volume_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean volume data."""
        try:
            if 'volume' in data.columns:
                # Remove negative volumes
                data.loc[data['volume'] < 0, 'volume'] = 0
                
                # Handle extremely low volumes (potential data errors)
                avg_volume = data['volume'].mean()
                min_volume_threshold = avg_volume * self.quality_thresholds['min_volume_ratio']
                
                low_volume_mask = (data['volume'] > 0) & (data['volume'] < min_volume_threshold)
                if low_volume_mask.any():
                    self.logger.info(f"Found {low_volume_mask.sum()} rows with unusually low volume")
                    # Set to NaN and interpolate
                    data.loc[low_volume_mask, 'volume'] = np.nan
                    data['volume'] = data['volume'].interpolate()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to clean volume data: {e}")
            return data
    
    def _remove_invalid_options(self, data: pd.DataFrame) -> pd.DataFrame:
        """Remove invalid options contracts."""
        try:
            initial_count = len(data)
            
            # Remove options with negative or zero prices
            if 'bid' in data.columns and 'ask' in data.columns:
                data = data[(data['bid'] >= 0) & (data['ask'] > 0)]
                data = data[data['ask'] >= data['bid']]  # Ask should be >= bid
            
            # Remove options with invalid strikes
            if 'strike' in data.columns:
                data = data[data['strike'] > 0]
            
            # Remove expired options
            if 'expiration_date' in data.columns:
                current_date = datetime.now().date()
                data = data[pd.to_datetime(data['expiration_date']).dt.date >= current_date]
            
            removed_count = initial_count - len(data)
            if removed_count > 0:
                self.logger.info(f"Removed {removed_count} invalid options")
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to remove invalid options: {e}")
            return data
    
    def _validate_greeks(self, data: pd.DataFrame) -> pd.DataFrame:
        """Validate options Greeks."""
        try:
            greek_columns = ['delta', 'gamma', 'theta', 'vega', 'rho']
            
            for greek in greek_columns:
                if greek in data.columns:
                    # Remove extreme values
                    if greek == 'delta':
                        # Delta should be between -1 and 1
                        data.loc[(data[greek] < -1) | (data[greek] > 1), greek] = np.nan
                    elif greek == 'gamma':
                        # Gamma should be positive
                        data.loc[data[greek] < 0, greek] = np.nan
                    elif greek == 'vega':
                        # Vega should be positive
                        data.loc[data[greek] < 0, greek] = np.nan
                    
                    # Interpolate missing values
                    data[greek] = data[greek].interpolate()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to validate Greeks: {e}")
            return data
    
    def _clean_implied_volatility(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean implied volatility data."""
        try:
            if 'implied_volatility' in data.columns:
                # Remove negative or extremely high IV
                data.loc[(data['implied_volatility'] < 0) | (data['implied_volatility'] > 5), 'implied_volatility'] = np.nan
                
                # Interpolate missing values
                data['implied_volatility'] = data['implied_volatility'].interpolate()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to clean implied volatility: {e}")
            return data
    
    def _validate_option_prices(self, data: pd.DataFrame) -> pd.DataFrame:
        """Validate option prices against intrinsic value."""
        try:
            if all(col in data.columns for col in ['strike', 'underlying_price', 'option_type']):
                # Calculate intrinsic value
                calls_mask = data['option_type'].str.lower() == 'call'
                puts_mask = data['option_type'].str.lower() == 'put'
                
                intrinsic_value = pd.Series(0.0, index=data.index)
                intrinsic_value[calls_mask] = np.maximum(0, data.loc[calls_mask, 'underlying_price'] - data.loc[calls_mask, 'strike'])
                intrinsic_value[puts_mask] = np.maximum(0, data.loc[puts_mask, 'strike'] - data.loc[puts_mask, 'underlying_price'])
                
                # Check if option prices are below intrinsic value
                if 'last_price' in data.columns:
                    invalid_prices = data['last_price'] < intrinsic_value
                    if invalid_prices.any():
                        self.logger.warning(f"Found {invalid_prices.sum()} options priced below intrinsic value")
                        # Set to intrinsic value
                        data.loc[invalid_prices, 'last_price'] = intrinsic_value[invalid_prices]
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to validate option prices: {e}")
            return data
    
    def _convert_numeric_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert string numeric fields to float."""
        try:
            numeric_fields = [
                'market_cap', 'enterprise_value', 'pe_ratio', 'forward_pe', 'peg_ratio',
                'price_to_book', 'price_to_sales', 'profit_margins', 'operating_margins',
                'return_on_assets', 'return_on_equity', 'revenue_growth', 'earnings_growth',
                'debt_to_equity', 'current_ratio', 'quick_ratio', 'dividend_yield', 'beta'
            ]
            
            for field in numeric_fields:
                if field in data and data[field] is not None:
                    try:
                        # Handle string values like "1.23B", "456M", etc.
                        value = str(data[field]).replace(',', '')
                        
                        if value.upper().endswith('B'):
                            data[field] = float(value[:-1]) * 1e9
                        elif value.upper().endswith('M'):
                            data[field] = float(value[:-1]) * 1e6
                        elif value.upper().endswith('K'):
                            data[field] = float(value[:-1]) * 1e3
                        else:
                            data[field] = float(value)
                    except (ValueError, TypeError):
                        data[field] = None
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to convert numeric fields: {e}")
            return data
    
    def _clean_fundamental_values(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean fundamental data values."""
        try:
            # Remove None, NaN, and invalid values
            cleaned_data = {}
            
            for key, value in data.items():
                if value is not None and not (isinstance(value, float) and np.isnan(value)):
                    if isinstance(value, (int, float)):
                        # Check for reasonable ranges
                        if key in ['pe_ratio', 'forward_pe'] and (value < 0 or value > 1000):
                            continue
                        elif key in ['profit_margins', 'operating_margins'] and (value < -1 or value > 1):
                            continue
                        elif key in ['return_on_assets', 'return_on_equity'] and (value < -1 or value > 1):
                            continue
                    
                    cleaned_data[key] = value
            
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"Failed to clean fundamental values: {e}")
            return data
    
    def _validate_fundamental_ratios(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate fundamental ratios for consistency."""
        try:
            # Add validation logic for fundamental ratios
            # For example, check if P/E ratio is consistent with price and earnings
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to validate fundamental ratios: {e}")
            return data


# Global data cleaner instance
data_cleaner = DataCleaner()
