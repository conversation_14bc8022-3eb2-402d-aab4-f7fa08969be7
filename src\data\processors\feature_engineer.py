"""
Feature Engineer for QuantEdgeFlow
Advanced feature engineering for machine learning and analytics
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import warnings

from src.core.analytics.technical_indicators import TechnicalIndicators

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


class FeatureEngineer:
    """Advanced feature engineering for trading algorithms."""
    
    def __init__(self):
        self.logger = logger
        self.technical_indicators = TechnicalIndicators()
        
        # Feature configuration
        self.feature_config = {
            'lookback_periods': [5, 10, 20, 50],
            'volatility_windows': [5, 10, 20],
            'momentum_periods': [1, 5, 10, 20],
            'ma_periods': [5, 10, 20, 50, 200]
        }
    
    def create_market_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive market-based features."""
        try:
            if data.empty or 'close' not in data.columns:
                return pd.DataFrame()
            
            features = pd.DataFrame(index=data.index)
            
            # Price-based features
            features = pd.concat([features, self._create_price_features(data)], axis=1)
            
            # Technical indicator features
            features = pd.concat([features, self._create_technical_features(data)], axis=1)
            
            # Volatility features
            features = pd.concat([features, self._create_volatility_features(data)], axis=1)
            
            # Volume features
            if 'volume' in data.columns:
                features = pd.concat([features, self._create_volume_features(data)], axis=1)
            
            # Time-based features
            features = pd.concat([features, self._create_time_features(data)], axis=1)
            
            # Lag features
            features = pd.concat([features, self._create_lag_features(data)], axis=1)
            
            return features.fillna(0)
            
        except Exception as e:
            self.logger.error(f"Market feature creation failed: {e}")
            return pd.DataFrame()
    
    def create_options_features(self, options_data: pd.DataFrame, market_data: pd.DataFrame) -> pd.DataFrame:
        """Create options-specific features."""
        try:
            if options_data.empty:
                return pd.DataFrame()
            
            features = pd.DataFrame(index=options_data.index)
            
            # Greeks-based features
            features = pd.concat([features, self._create_greeks_features(options_data)], axis=1)
            
            # Implied volatility features
            features = pd.concat([features, self._create_iv_features(options_data)], axis=1)
            
            # Moneyness features
            features = pd.concat([features, self._create_moneyness_features(options_data)], axis=1)
            
            # Time decay features
            features = pd.concat([features, self._create_time_decay_features(options_data)], axis=1)
            
            return features.fillna(0)
            
        except Exception as e:
            self.logger.error(f"Options feature creation failed: {e}")
            return pd.DataFrame()
    
    def _create_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create price-based features."""
        features = pd.DataFrame(index=data.index)
        
        # Returns
        for period in self.feature_config['momentum_periods']:
            features[f'return_{period}d'] = data['close'].pct_change(period)
        
        # Price ratios
        for period in self.feature_config['ma_periods']:
            ma = data['close'].rolling(period).mean()
            features[f'price_ma{period}_ratio'] = data['close'] / ma
        
        # High-low ratios
        if all(col in data.columns for col in ['high', 'low']):
            features['hl_ratio'] = data['high'] / data['low']
            features['close_hl_position'] = (data['close'] - data['low']) / (data['high'] - data['low'])
        
        return features
    
    def _create_technical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create technical indicator features."""
        features = pd.DataFrame(index=data.index)
        
        # RSI
        features['rsi_14'] = self.technical_indicators.rsi(data['close'], 14)
        features['rsi_30'] = self.technical_indicators.rsi(data['close'], 30)
        
        # Moving averages
        for period in [5, 10, 20, 50]:
            features[f'sma_{period}'] = self.technical_indicators.sma(data['close'], period)
            features[f'ema_{period}'] = self.technical_indicators.ema(data['close'], period)
        
        # MACD
        macd_line, signal_line, histogram = self.technical_indicators.macd(data['close'])
        features['macd'] = macd_line
        features['macd_signal'] = signal_line
        features['macd_histogram'] = histogram
        
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = self.technical_indicators.bollinger_bands(data['close'], 20, 2)
        features['bb_upper'] = bb_upper
        features['bb_middle'] = bb_middle
        features['bb_lower'] = bb_lower
        features['bb_position'] = (data['close'] - bb_lower) / (bb_upper - bb_lower)
        features['bb_width'] = (bb_upper - bb_lower) / bb_middle
        
        return features
    
    def _create_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create volatility-based features."""
        features = pd.DataFrame(index=data.index)
        
        returns = data['close'].pct_change()
        
        # Rolling volatility
        for window in self.feature_config['volatility_windows']:
            features[f'volatility_{window}d'] = returns.rolling(window).std() * np.sqrt(252)
        
        # Volatility ratios
        vol_5 = returns.rolling(5).std()
        vol_20 = returns.rolling(20).std()
        features['vol_ratio_5_20'] = vol_5 / vol_20
        
        # Realized volatility
        features['realized_vol_20'] = returns.rolling(20).apply(lambda x: np.sqrt(np.sum(x**2) * 252))
        
        return features
    
    def _create_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create volume-based features."""
        features = pd.DataFrame(index=data.index)
        
        # Volume moving averages
        for period in [5, 10, 20]:
            features[f'volume_ma_{period}'] = data['volume'].rolling(period).mean()
            features[f'volume_ratio_{period}'] = data['volume'] / features[f'volume_ma_{period}']
        
        # Volume price trend
        features['vpt'] = self.technical_indicators.volume_price_trend(data['close'], data['volume'])
        
        # On-balance volume
        features['obv'] = self.technical_indicators.on_balance_volume(data['close'], data['volume'])
        
        return features
    
    def _create_time_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create time-based features."""
        features = pd.DataFrame(index=data.index)
        
        if hasattr(data.index, 'dayofweek'):
            features['day_of_week'] = data.index.dayofweek
            features['month'] = data.index.month
            features['quarter'] = data.index.quarter
        
        return features
    
    def _create_lag_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create lagged features."""
        features = pd.DataFrame(index=data.index)
        
        # Lagged returns
        returns = data['close'].pct_change()
        for lag in [1, 2, 3, 5]:
            features[f'return_lag_{lag}'] = returns.shift(lag)
        
        # Lagged prices
        for lag in [1, 5, 10]:
            features[f'close_lag_{lag}'] = data['close'].shift(lag)
        
        return features
    
    def _create_greeks_features(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """Create Greeks-based features."""
        features = pd.DataFrame(index=options_data.index)
        
        greek_columns = ['delta', 'gamma', 'theta', 'vega', 'rho']
        
        for greek in greek_columns:
            if greek in options_data.columns:
                features[f'{greek}'] = options_data[greek]
                features[f'{greek}_change'] = options_data[greek].pct_change()
                features[f'{greek}_ma_5'] = options_data[greek].rolling(5).mean()
        
        return features
    
    def _create_iv_features(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """Create implied volatility features."""
        features = pd.DataFrame(index=options_data.index)
        
        if 'implied_volatility' in options_data.columns:
            iv = options_data['implied_volatility']
            
            features['iv'] = iv
            features['iv_change'] = iv.pct_change()
            features['iv_ma_5'] = iv.rolling(5).mean()
            features['iv_ma_20'] = iv.rolling(20).mean()
            features['iv_rank'] = iv.rolling(252).rank(pct=True)
            features['iv_percentile'] = iv.rolling(252).apply(
                lambda x: (x.iloc[-1] - x.min()) / (x.max() - x.min()) if x.max() != x.min() else 0.5
            )
        
        return features
    
    def _create_moneyness_features(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """Create moneyness-based features."""
        features = pd.DataFrame(index=options_data.index)
        
        if all(col in options_data.columns for col in ['strike', 'underlying_price']):
            moneyness = options_data['strike'] / options_data['underlying_price']
            features['moneyness'] = moneyness
            features['log_moneyness'] = np.log(moneyness)
            features['abs_log_moneyness'] = np.abs(np.log(moneyness))
        
        return features
    
    def _create_time_decay_features(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """Create time decay features."""
        features = pd.DataFrame(index=options_data.index)
        
        if 'days_to_expiry' in options_data.columns:
            dte = options_data['days_to_expiry']
            features['days_to_expiry'] = dte
            features['time_to_expiry'] = dte / 365.0
            features['sqrt_time_to_expiry'] = np.sqrt(dte / 365.0)
            features['time_decay_factor'] = np.exp(-dte / 30.0)  # 30-day decay
        
        return features
    
    def create_interaction_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Create interaction features between existing features."""
        try:
            interaction_features = pd.DataFrame(index=features.index)
            
            # Select key features for interactions
            key_features = ['return_1d', 'volatility_20d', 'rsi_14', 'volume_ratio_20']
            available_features = [f for f in key_features if f in features.columns]
            
            # Create pairwise interactions
            for i, feat1 in enumerate(available_features):
                for feat2 in available_features[i+1:]:
                    interaction_features[f'{feat1}_x_{feat2}'] = features[feat1] * features[feat2]
            
            return interaction_features
            
        except Exception as e:
            self.logger.error(f"Interaction feature creation failed: {e}")
            return pd.DataFrame()
    
    def select_features(self, features: pd.DataFrame, target: pd.Series, method: str = 'correlation') -> List[str]:
        """Select most relevant features."""
        try:
            if features.empty or target.empty:
                return []
            
            # Align features and target
            common_index = features.index.intersection(target.index)
            features_aligned = features.loc[common_index]
            target_aligned = target.loc[common_index]
            
            if method == 'correlation':
                # Select features based on correlation with target
                correlations = features_aligned.corrwith(target_aligned).abs()
                selected_features = correlations.nlargest(20).index.tolist()
            else:
                # Default: return all features
                selected_features = features.columns.tolist()
            
            return selected_features
            
        except Exception as e:
            self.logger.error(f"Feature selection failed: {e}")
            return features.columns.tolist() if not features.empty else []


# Global feature engineer instance
feature_engineer = FeatureEngineer()
