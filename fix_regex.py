#!/usr/bin/env python3
"""
Quick script to fix regex parameters in Pydantic models for v2 compatibility
"""

import os
import re
from pathlib import Path

def fix_regex_in_file(file_path: Path):
    """Fix regex parameters in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Replace regex= with pattern=
        content = re.sub(r'\bregex=', 'pattern=', content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed regex parameters in {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix all regex parameters."""
    src_dir = Path("src")
    
    if not src_dir.exists():
        print("❌ src directory not found!")
        return
    
    print("🔧 Fixing regex parameters in Pydantic models...")
    
    fixed_count = 0
    total_count = 0
    
    # Find all Python files
    for py_file in src_dir.rglob("*.py"):
        total_count += 1
        if fix_regex_in_file(py_file):
            fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total files processed: {total_count}")
    print(f"   Files with fixes: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print("\n✅ Regex parameter fixes completed!")
    else:
        print("\n✅ No regex parameter fixes needed!")

if __name__ == "__main__":
    main()
