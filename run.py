#!/usr/bin/env python3
"""
QuantEdgeFlow Application Runner
Main entry point for running the QuantEdgeFlow application
"""

import asyncio
import logging
import sys
import uvicorn
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config.settings import get_settings
from config.logging_config import setup_logging
from scripts.start import main as startup_main

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

settings = get_settings()


def run_development():
    """Run in development mode with auto-reload."""
    logger.info("🚀 Starting QuantEdgeFlow in DEVELOPMENT mode")
    
    uvicorn.run(
        "src.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=True,
        reload_dirs=["src"],
        log_level="debug" if settings.DEBUG else "info",
        access_log=True,
        use_colors=True,
        loop="uvloop" if sys.platform != "win32" else "asyncio"
    )


def run_production():
    """Run in production mode."""
    logger.info("🚀 Starting QuantEdgeFlow in PRODUCTION mode")
    
    uvicorn.run(
        "src.main:app",
        host=settings.HOST,
        port=settings.PORT,
        workers=4,
        log_level="info",
        access_log=True,
        use_colors=False,
        loop="uvloop" if sys.platform != "win32" else "asyncio",
        proxy_headers=True,
        forwarded_allow_ips="*"
    )


async def run_with_startup():
    """Run application with startup checks."""
    try:
        # Run startup checks
        await startup_main()
        
        # Start the application
        if settings.DEBUG:
            run_development()
        else:
            run_production()
            
    except Exception as e:
        logger.error(f"💥 Failed to start application: {e}")
        sys.exit(1)


def main():
    """Main entry point."""
    try:
        # Run startup and application
        asyncio.run(run_with_startup())
        
    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
