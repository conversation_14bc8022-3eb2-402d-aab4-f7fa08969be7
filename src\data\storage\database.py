"""
Database Manager for QuantEdgeFlow
Advanced database operations and query optimization
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, date
import pandas as pd
from sqlalchemy import text, select, insert, update, delete
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from config.database import get_async_session, get_sync_session
from core.models.market_data import StockQuote, HistoricalPrice
from core.models.options import OptionContract, OptionChain
from core.models.portfolio import Portfolio, PortfolioPosition
from core.models.trades import Trade, TradeLeg

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Advanced database operations manager."""
    
    def __init__(self):
        self.logger = logger
    
    async def save_market_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """Save market data to database."""
        try:
            async with get_async_session() as session:
                records = []
                
                for _, row in data.iterrows():
                    record = StockQuote(
                        symbol=symbol,
                        timestamp=row.name if hasattr(row, 'name') else datetime.utcnow(),
                        open_price=float(row.get('open', 0)),
                        high_price=float(row.get('high', 0)),
                        low_price=float(row.get('low', 0)),
                        close_price=float(row.get('close', 0)),
                        volume=int(row.get('volume', 0)),
                        adjusted_close=float(row.get('adj_close', row.get('close', 0)))
                    )
                    records.append(record)
                
                session.add_all(records)
                await session.commit()
                
                self.logger.info(f"Saved {len(records)} market data records for {symbol}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to save market data: {e}")
            return False
    
    async def get_market_data(
        self, 
        symbol: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 1000
    ) -> pd.DataFrame:
        """Retrieve market data from database."""
        try:
            async with get_async_session() as session:
                query = select(StockQuote).where(StockQuote.symbol == symbol)
                
                if start_date:
                    query = query.where(StockQuote.timestamp >= start_date)
                if end_date:
                    query = query.where(StockQuote.timestamp <= end_date)
                
                query = query.order_by(StockQuote.timestamp.desc()).limit(limit)
                
                result = await session.execute(query)
                records = result.scalars().all()
                
                if not records:
                    return pd.DataFrame()
                
                # Convert to DataFrame
                data = []
                for record in records:
                    data.append({
                        'timestamp': record.timestamp,
                        'open': record.open_price,
                        'high': record.high_price,
                        'low': record.low_price,
                        'close': record.close_price,
                        'volume': record.volume,
                        'adj_close': record.adjusted_close
                    })
                
                df = pd.DataFrame(data)
                df.set_index('timestamp', inplace=True)
                df.sort_index(inplace=True)
                
                return df
                
        except Exception as e:
            self.logger.error(f"Failed to get market data: {e}")
            return pd.DataFrame()
    
    async def save_options_data(self, options_data: List[Dict[str, Any]]) -> bool:
        """Save options data to database."""
        try:
            async with get_async_session() as session:
                records = []
                
                for option in options_data:
                    record = OptionContract(
                        symbol=option['symbol'],
                        option_symbol=option['option_symbol'],
                        option_type=option['option_type'],
                        strike_price=float(option['strike_price']),
                        expiration_date=option['expiration_date'],
                        bid_price=float(option.get('bid', 0)),
                        ask_price=float(option.get('ask', 0)),
                        last_price=float(option.get('last_price', 0)),
                        volume=int(option.get('volume', 0)),
                        open_interest=int(option.get('open_interest', 0)),
                        implied_volatility=float(option.get('implied_volatility', 0)),
                        delta=float(option.get('delta', 0)),
                        gamma=float(option.get('gamma', 0)),
                        theta=float(option.get('theta', 0)),
                        vega=float(option.get('vega', 0)),
                        rho=float(option.get('rho', 0))
                    )
                    records.append(record)
                
                session.add_all(records)
                await session.commit()
                
                self.logger.info(f"Saved {len(records)} options records")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to save options data: {e}")
            return False
    
    async def get_options_chain(
        self, 
        symbol: str,
        expiration_date: Optional[date] = None
    ) -> pd.DataFrame:
        """Retrieve options chain from database."""
        try:
            async with get_async_session() as session:
                query = select(OptionContract).where(OptionContract.symbol == symbol)
                
                if expiration_date:
                    query = query.where(OptionContract.expiration_date == expiration_date)
                
                query = query.order_by(OptionContract.strike_price)
                
                result = await session.execute(query)
                records = result.scalars().all()
                
                if not records:
                    return pd.DataFrame()
                
                # Convert to DataFrame
                data = []
                for record in records:
                    data.append({
                        'option_symbol': record.option_symbol,
                        'option_type': record.option_type,
                        'strike_price': record.strike_price,
                        'expiration_date': record.expiration_date,
                        'bid': record.bid_price,
                        'ask': record.ask_price,
                        'last_price': record.last_price,
                        'volume': record.volume,
                        'open_interest': record.open_interest,
                        'implied_volatility': record.implied_volatility,
                        'delta': record.delta,
                        'gamma': record.gamma,
                        'theta': record.theta,
                        'vega': record.vega,
                        'rho': record.rho
                    })
                
                return pd.DataFrame(data)
                
        except Exception as e:
            self.logger.error(f"Failed to get options chain: {e}")
            return pd.DataFrame()
    
    async def save_portfolio(self, portfolio_data: Dict[str, Any]) -> Optional[str]:
        """Save portfolio to database."""
        try:
            async with get_async_session() as session:
                portfolio = Portfolio(
                    user_id=portfolio_data['user_id'],
                    name=portfolio_data['name'],
                    description=portfolio_data.get('description', ''),
                    nav=float(portfolio_data['nav']),
                    cash_balance=float(portfolio_data['cash_balance']),
                    initial_capital=float(portfolio_data['initial_capital'])
                )
                
                session.add(portfolio)
                await session.commit()
                await session.refresh(portfolio)
                
                self.logger.info(f"Saved portfolio {portfolio.id}")
                return str(portfolio.id)
                
        except Exception as e:
            self.logger.error(f"Failed to save portfolio: {e}")
            return None
    
    async def get_portfolio(self, portfolio_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve portfolio from database."""
        try:
            async with get_async_session() as session:
                query = select(Portfolio).where(Portfolio.id == portfolio_id)
                result = await session.execute(query)
                portfolio = result.scalar_one_or_none()
                
                if not portfolio:
                    return None
                
                return {
                    'id': str(portfolio.id),
                    'user_id': portfolio.user_id,
                    'name': portfolio.name,
                    'description': portfolio.description,
                    'nav': float(portfolio.nav),
                    'cash_balance': float(portfolio.cash_balance),
                    'initial_capital': float(portfolio.initial_capital),
                    'created_at': portfolio.created_at,
                    'updated_at': portfolio.updated_at
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get portfolio: {e}")
            return None
    
    async def save_trade(self, trade_data: Dict[str, Any]) -> Optional[str]:
        """Save trade to database."""
        try:
            async with get_async_session() as session:
                trade = Trade(
                    portfolio_id=trade_data['portfolio_id'],
                    symbol=trade_data['symbol'],
                    trade_type=trade_data['trade_type'],
                    quantity=int(trade_data['quantity']),
                    price=float(trade_data['price']),
                    status=trade_data.get('status', 'pending'),
                    strategy=trade_data.get('strategy'),
                    notes=trade_data.get('notes', '')
                )
                
                session.add(trade)
                await session.commit()
                await session.refresh(trade)
                
                self.logger.info(f"Saved trade {trade.id}")
                return str(trade.id)
                
        except Exception as e:
            self.logger.error(f"Failed to save trade: {e}")
            return None
    
    async def get_trades(
        self, 
        portfolio_id: Optional[str] = None,
        symbol: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Retrieve trades from database."""
        try:
            async with get_async_session() as session:
                query = select(Trade)
                
                if portfolio_id:
                    query = query.where(Trade.portfolio_id == portfolio_id)
                if symbol:
                    query = query.where(Trade.symbol == symbol)
                
                query = query.order_by(Trade.created_at.desc()).limit(limit)
                
                result = await session.execute(query)
                trades = result.scalars().all()
                
                trade_list = []
                for trade in trades:
                    trade_list.append({
                        'id': str(trade.id),
                        'portfolio_id': str(trade.portfolio_id),
                        'symbol': trade.symbol,
                        'trade_type': trade.trade_type,
                        'quantity': trade.quantity,
                        'price': float(trade.price),
                        'status': trade.status,
                        'strategy': trade.strategy,
                        'notes': trade.notes,
                        'created_at': trade.created_at,
                        'updated_at': trade.updated_at
                    })
                
                return trade_list
                
        except Exception as e:
            self.logger.error(f"Failed to get trades: {e}")
            return []
    
    async def execute_raw_query(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute raw SQL query."""
        try:
            async with get_async_session() as session:
                result = await session.execute(text(query), params or {})
                
                # Convert result to list of dictionaries
                columns = result.keys()
                rows = result.fetchall()
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Failed to execute raw query: {e}")
            return []
    
    async def bulk_insert(self, table_name: str, data: List[Dict[str, Any]]) -> bool:
        """Perform bulk insert operation."""
        try:
            if not data:
                return True
            
            async with get_async_session() as session:
                # Use bulk insert for better performance
                await session.execute(
                    text(f"INSERT INTO {table_name} ({', '.join(data[0].keys())}) VALUES {', '.join(['(' + ', '.join([':' + k + str(i) for k in data[0].keys()]) + ')' for i in range(len(data))])}"),
                    {f"{k}{i}": v for i, row in enumerate(data) for k, v in row.items()}
                )
                await session.commit()
                
                self.logger.info(f"Bulk inserted {len(data)} records into {table_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to bulk insert: {e}")
            return False
    
    async def cleanup_old_data(self, table_name: str, date_column: str, days_to_keep: int) -> bool:
        """Clean up old data from database."""
        try:
            cutoff_date = datetime.utcnow() - pd.Timedelta(days=days_to_keep)
            
            async with get_async_session() as session:
                query = text(f"DELETE FROM {table_name} WHERE {date_column} < :cutoff_date")
                result = await session.execute(query, {"cutoff_date": cutoff_date})
                await session.commit()
                
                deleted_count = result.rowcount
                self.logger.info(f"Cleaned up {deleted_count} old records from {table_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old data: {e}")
            return False


# Global database manager instance
database_manager = DatabaseManager()
