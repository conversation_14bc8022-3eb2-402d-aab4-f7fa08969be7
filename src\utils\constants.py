"""
Constants for QuantEdgeFlow
Application-wide constants and configuration values
"""

from enum import Enum
from typing import Dict, List, Tuple


# API Configuration
API_VERSION = "v1"
API_PREFIX = f"/api/{API_VERSION}"
MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB
DEFAULT_PAGE_SIZE = 50
MAX_PAGE_SIZE = 500

# Database Configuration
DEFAULT_POOL_SIZE = 20
DEFAULT_MAX_OVERFLOW = 30
CONNECTION_TIMEOUT = 30
QUERY_TIMEOUT = 60

# Cache Configuration
DEFAULT_CACHE_TTL = 300  # 5 minutes
LONG_CACHE_TTL = 3600   # 1 hour
SHORT_CACHE_TTL = 60    # 1 minute

# Market Data Configuration
MARKET_DATA_REFRESH_INTERVAL = 30  # seconds
OPTIONS_DATA_REFRESH_INTERVAL = 300  # 5 minutes
FUNDAMENTAL_DATA_REFRESH_INTERVAL = 3600  # 1 hour

# Trading Configuration
DEFAULT_COMMISSION = 1.00  # USD per trade
MAX_POSITION_SIZE = 0.2  # 20% of portfolio
DEFAULT_SLIPPAGE = 0.001  # 0.1%
MAX_DAILY_TRADES = 100
TRADING_DAYS_PER_YEAR = 252  # Standard trading days per year

# Risk Management
DEFAULT_RISK_FREE_RATE = 0.05  # 5%
DEFAULT_VOLATILITY = 0.25  # 25%
MAX_PORTFOLIO_LEVERAGE = 2.0
VaR_CONFIDENCE_LEVELS = [0.95, 0.99]
STRESS_TEST_SCENARIOS = ["2008_CRISIS", "COVID_2020", "TECH_BUBBLE"]

# Options Configuration
OPTION_EXPIRATION_BUFFER_DAYS = 7  # Don't trade options expiring within 7 days
MIN_OPTION_VOLUME = 10
MIN_OPTION_OPEN_INTEREST = 100
MAX_OPTION_SPREAD_PERCENT = 0.05  # 5%

# Greeks Thresholds
DELTA_NEUTRAL_THRESHOLD = 0.1
GAMMA_RISK_THRESHOLD = 100
THETA_DECAY_THRESHOLD = -50
VEGA_RISK_THRESHOLD = 1000

# Market Hours (Eastern Time)
MARKET_OPEN_TIME = "09:30"
MARKET_CLOSE_TIME = "16:00"
PRE_MARKET_OPEN_TIME = "04:00"
AFTER_HOURS_CLOSE_TIME = "20:00"

# Supported Exchanges
SUPPORTED_EXCHANGES = [
    "NYSE", "NASDAQ", "AMEX", "BATS", "IEX", "ARCA"
]

# Asset Classes
class AssetClass(str, Enum):
    EQUITY = "equity"
    OPTION = "option"
    FUTURE = "future"
    BOND = "bond"
    FOREX = "forex"
    CRYPTO = "crypto"
    ETF = "etf"
    MUTUAL_FUND = "mutual_fund"


# Option Types
class OptionType(str, Enum):
    CALL = "call"
    PUT = "put"


# Order Types
class OrderType(str, Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"
    BRACKET = "bracket"
    OCO = "oco"  # One-Cancels-Other


# Time in Force
class TimeInForce(str, Enum):
    DAY = "day"
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate or Cancel
    FOK = "fok"  # Fill or Kill
    GTD = "gtd"  # Good Till Date


# Trading Strategies
OPTION_STRATEGIES = {
    "COVERED_CALL": {
        "name": "Covered Call",
        "description": "Long stock + short call",
        "legs": 2,
        "risk_profile": "limited_upside",
        "market_outlook": "neutral_to_bullish"
    },
    "CASH_SECURED_PUT": {
        "name": "Cash Secured Put",
        "description": "Short put with cash collateral",
        "legs": 1,
        "risk_profile": "limited_downside",
        "market_outlook": "neutral_to_bullish"
    },
    "IRON_CONDOR": {
        "name": "Iron Condor",
        "description": "Short call spread + short put spread",
        "legs": 4,
        "risk_profile": "limited_risk_reward",
        "market_outlook": "neutral"
    },
    "BUTTERFLY": {
        "name": "Butterfly Spread",
        "description": "Long 1 ATM, short 2 OTM, long 1 further OTM",
        "legs": 3,
        "risk_profile": "limited_risk_reward",
        "market_outlook": "neutral"
    },
    "STRADDLE": {
        "name": "Long Straddle",
        "description": "Long call + long put at same strike",
        "legs": 2,
        "risk_profile": "unlimited_upside_limited_downside",
        "market_outlook": "high_volatility"
    },
    "STRANGLE": {
        "name": "Long Strangle",
        "description": "Long call + long put at different strikes",
        "legs": 2,
        "risk_profile": "unlimited_upside_limited_downside",
        "market_outlook": "high_volatility"
    },
    "COLLAR": {
        "name": "Protective Collar",
        "description": "Long stock + long put + short call",
        "legs": 3,
        "risk_profile": "limited_risk_reward",
        "market_outlook": "neutral"
    }
}

# Technical Indicators
TECHNICAL_INDICATORS = [
    "SMA", "EMA", "RSI", "MACD", "BOLLINGER_BANDS",
    "STOCHASTIC", "ATR", "ADX", "CCI", "WILLIAMS_R"
]

# Sector Classifications (GICS)
SECTORS = {
    "10": "Energy",
    "15": "Materials",
    "20": "Industrials",
    "25": "Consumer Discretionary",
    "30": "Consumer Staples",
    "35": "Health Care",
    "40": "Financials",
    "45": "Information Technology",
    "50": "Communication Services",
    "55": "Utilities",
    "60": "Real Estate"
}

# Market Cap Classifications
MARKET_CAP_RANGES = {
    "NANO": (0, 50_000_000),           # < $50M
    "MICRO": (50_000_000, 300_000_000),    # $50M - $300M
    "SMALL": (300_000_000, 2_000_000_000), # $300M - $2B
    "MID": (2_000_000_000, 10_000_000_000), # $2B - $10B
    "LARGE": (10_000_000_000, 200_000_000_000), # $10B - $200B
    "MEGA": (200_000_000_000, float('inf'))  # > $200B
}

# Volatility Classifications
VOLATILITY_RANGES = {
    "LOW": (0, 0.15),      # < 15%
    "NORMAL": (0.15, 0.25), # 15% - 25%
    "HIGH": (0.25, 0.40),   # 25% - 40%
    "EXTREME": (0.40, float('inf'))  # > 40%
}

# Performance Metrics Thresholds
PERFORMANCE_THRESHOLDS = {
    "SHARPE_RATIO": {
        "EXCELLENT": 2.0,
        "GOOD": 1.0,
        "ACCEPTABLE": 0.5,
        "POOR": 0.0
    },
    "MAX_DRAWDOWN": {
        "EXCELLENT": 0.05,  # < 5%
        "GOOD": 0.10,       # < 10%
        "ACCEPTABLE": 0.20,  # < 20%
        "POOR": 0.30        # < 30%
    },
    "WIN_RATE": {
        "EXCELLENT": 0.70,  # > 70%
        "GOOD": 0.60,       # > 60%
        "ACCEPTABLE": 0.50,  # > 50%
        "POOR": 0.40        # > 40%
    }
}

# Data Sources
DATA_SOURCES = {
    "YAHOO_FINANCE": {
        "name": "Yahoo Finance",
        "type": "free",
        "rate_limit": 2000,  # requests per hour
        "supports": ["stocks", "options", "fundamentals"]
    },
    "ALPHA_VANTAGE": {
        "name": "Alpha Vantage",
        "type": "freemium",
        "rate_limit": 5,     # requests per minute
        "supports": ["stocks", "fundamentals", "forex"]
    },
    "POLYGON": {
        "name": "Polygon.io",
        "type": "paid",
        "rate_limit": 100,   # requests per minute
        "supports": ["stocks", "options", "forex", "crypto"]
    },
    "IEX": {
        "name": "IEX Cloud",
        "type": "freemium",
        "rate_limit": 100,   # requests per second
        "supports": ["stocks", "fundamentals"]
    }
}

# Error Codes
ERROR_CODES = {
    # Authentication Errors (1000-1099)
    "AUTH_INVALID_TOKEN": 1001,
    "AUTH_TOKEN_EXPIRED": 1002,
    "AUTH_INSUFFICIENT_PERMISSIONS": 1003,
    
    # Validation Errors (1100-1199)
    "VALIDATION_INVALID_SYMBOL": 1101,
    "VALIDATION_INVALID_DATE_RANGE": 1102,
    "VALIDATION_INVALID_QUANTITY": 1103,
    "VALIDATION_INVALID_PRICE": 1104,
    
    # Trading Errors (1200-1299)
    "TRADE_INSUFFICIENT_FUNDS": 1201,
    "TRADE_POSITION_LIMIT_EXCEEDED": 1202,
    "TRADE_MARKET_CLOSED": 1203,
    "TRADE_INVALID_ORDER_TYPE": 1204,
    
    # Data Errors (1300-1399)
    "DATA_NOT_FOUND": 1301,
    "DATA_SOURCE_UNAVAILABLE": 1302,
    "DATA_RATE_LIMIT_EXCEEDED": 1303,
    
    # System Errors (1400-1499)
    "SYSTEM_DATABASE_ERROR": 1401,
    "SYSTEM_EXTERNAL_API_ERROR": 1402,
    "SYSTEM_CACHE_ERROR": 1403
}

# HTTP Status Codes with Descriptions
HTTP_STATUS_DESCRIPTIONS = {
    200: "OK",
    201: "Created",
    204: "No Content",
    400: "Bad Request",
    401: "Unauthorized",
    403: "Forbidden",
    404: "Not Found",
    409: "Conflict",
    422: "Unprocessable Entity",
    429: "Too Many Requests",
    500: "Internal Server Error",
    502: "Bad Gateway",
    503: "Service Unavailable"
}

# Logging Configuration
LOG_LEVELS = {
    "DEBUG": 10,
    "INFO": 20,
    "WARNING": 30,
    "ERROR": 40,
    "CRITICAL": 50
}

LOG_FORMATS = {
    "SIMPLE": "%(levelname)s - %(message)s",
    "DETAILED": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "JSON": '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s"}'
}

# File Extensions
ALLOWED_FILE_EXTENSIONS = {
    "CSV": [".csv"],
    "EXCEL": [".xlsx", ".xls"],
    "JSON": [".json"],
    "PDF": [".pdf"],
    "IMAGE": [".jpg", ".jpeg", ".png", ".gif"]
}

# Regex Patterns
REGEX_PATTERNS = {
    "SYMBOL": r"^[A-Z]{1,5}$",
    "EMAIL": r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
    "PHONE": r"^\+?1?[2-9]\d{2}[2-9]\d{2}\d{4}$",
    "UUID": r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
    "OPTION_SYMBOL": r"^[A-Z]{1,5}\d{6}[CP]\d{8}$"
}

# Default Values
DEFAULTS = {
    "PORTFOLIO_INITIAL_CAPITAL": 100000.00,  # $100,000
    "PORTFOLIO_CASH_PERCENTAGE": 0.05,       # 5% cash
    "RISK_TOLERANCE": "moderate",
    "NOTIFICATION_PREFERENCES": ["email", "in_app"],
    "TIMEZONE": "US/Eastern",
    "CURRENCY": "USD",
    "LANGUAGE": "en"
}

# Feature Flags
FEATURE_FLAGS = {
    "ENABLE_PAPER_TRADING": True,
    "ENABLE_LIVE_TRADING": False,
    "ENABLE_OPTIONS_TRADING": True,
    "ENABLE_CRYPTO_TRADING": False,
    "ENABLE_INTERNATIONAL_MARKETS": False,
    "ENABLE_ADVANCED_ANALYTICS": True,
    "ENABLE_SOCIAL_FEATURES": False,
    "ENABLE_API_ACCESS": True
}
