"""
Algorithms Package for QuantEdgeFlow
Advanced algorithms for options pricing, volatility modeling, and machine learning
"""

# Black-Scholes model
from .black_scholes import (
    BlackScholesCalculator,
    OptionParameters,
    OptionPricing
)

# Monte Carlo simulation
from .monte_carlo import (
    MonteCarloEngine,
    MonteCarloParameters,
    SimulationResult,
    monte_carlo_engine
)

# Volatility models
from .volatility_models import (
    VolatilityCalculator,
    VolatilityForecast,
    GARCHParameters,
    volatility_calculator
)

# Machine learning
from .machine_learning import (
    MLPredictor,
    MLModelResult,
    FeatureSet,
    ml_predictor
)

__all__ = [
    # Black-Scholes
    "BlackScholesCalculator",
    "OptionParameters",
    "OptionPricing",
    
    # Monte Carlo
    "MonteCarloEngine",
    "MonteCarloParameters",
    "SimulationResult",
    "monte_carlo_engine",
    
    # Volatility models
    "VolatilityCalculator",
    "VolatilityForecast",
    "GARCHParameters",
    "volatility_calculator",
    
    # Machine learning
    "MLPredictor",
    "MLModelResult",
    "FeatureSet",
    "ml_predictor"
]
