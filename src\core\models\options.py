"""
Options Data Models for QuantEdgeFlow
Comprehensive options pricing, Greeks, and chain management
"""

from datetime import datetime, date
from decimal import Decimal
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid

from sqlalchemy import (
    Column, String, Float, Integer, DateTime, Boolean, 
    Text, ForeignKey, Numeric, Date, JSON, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, validates
from pydantic import BaseModel, Field, validator
from pydantic.types import condecimal

from src.config.database import Base


class OptionType(str, Enum):
    """Option type enumeration."""
    CALL = "call"
    PUT = "put"


class OptionStyle(str, Enum):
    """Option style enumeration."""
    AMERICAN = "american"
    EUROPEAN = "european"


class OptionContract(Base):
    """Option contract database model."""
    
    __tablename__ = "option_contracts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Contract identification
    symbol = Column(String(20), nullable=False, index=True)  # Underlying symbol
    option_symbol = Column(String(50), nullable=False, unique=True, index=True)  # Full option symbol
    
    # Contract details
    option_type = Column(String(10), nullable=False)  # call, put
    strike_price = Column(Numeric(10, 4), nullable=False)
    expiration_date = Column(Date, nullable=False, index=True)
    days_to_expiration = Column(Integer)
    
    # Contract specifications
    contract_size = Column(Integer, default=100)  # Shares per contract
    style = Column(String(20), default=OptionStyle.AMERICAN)
    exchange = Column(String(10))
    
    # Pricing data
    bid = Column(Numeric(10, 4))
    ask = Column(Numeric(10, 4))
    last_price = Column(Numeric(10, 4))
    mark_price = Column(Numeric(10, 4))  # Mid-point of bid/ask
    
    # Volume and interest
    volume = Column(Integer, default=0)
    open_interest = Column(Integer, default=0)
    
    # Greeks
    delta = Column(Float)
    gamma = Column(Float)
    theta = Column(Float)
    vega = Column(Float)
    rho = Column(Float)
    
    # Volatility metrics
    implied_volatility = Column(Float)
    historical_volatility = Column(Float)
    
    # Risk metrics
    intrinsic_value = Column(Numeric(10, 4))
    time_value = Column(Numeric(10, 4))
    moneyness = Column(Float)  # S/K for calls, K/S for puts
    
    # Market data timestamps
    last_trade_time = Column(DateTime)
    quote_time = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    chain = relationship("OptionChain", back_populates="contracts")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_option_symbol_expiration', 'symbol', 'expiration_date'),
        Index('idx_option_strike_type', 'strike_price', 'option_type'),
        Index('idx_option_expiration_dte', 'expiration_date', 'days_to_expiration'),
    )
    
    @validates('option_type')
    def validate_option_type(self, key, value):
        """Validate option type."""
        if value not in [OptionType.CALL, OptionType.PUT]:
            raise ValueError(f"Option type must be {OptionType.CALL} or {OptionType.PUT}")
        return value
    
    def calculate_intrinsic_value(self, underlying_price: Decimal) -> Decimal:
        """Calculate intrinsic value."""
        if self.option_type == OptionType.CALL:
            return max(Decimal(0), underlying_price - self.strike_price)
        else:  # PUT
            return max(Decimal(0), self.strike_price - underlying_price)
    
    def calculate_time_value(self, underlying_price: Decimal) -> Decimal:
        """Calculate time value."""
        if not self.mark_price:
            return Decimal(0)
        intrinsic = self.calculate_intrinsic_value(underlying_price)
        return max(Decimal(0), self.mark_price - intrinsic)
    
    def is_in_the_money(self, underlying_price: Decimal) -> bool:
        """Check if option is in the money."""
        if self.option_type == OptionType.CALL:
            return underlying_price > self.strike_price
        else:  # PUT
            return underlying_price < self.strike_price
    
    def calculate_moneyness(self, underlying_price: Decimal) -> float:
        """Calculate moneyness ratio."""
        if self.option_type == OptionType.CALL:
            return float(underlying_price / self.strike_price)
        else:  # PUT
            return float(self.strike_price / underlying_price)


class OptionChain(Base):
    """Option chain database model."""
    
    __tablename__ = "option_chains"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Chain identification
    symbol = Column(String(20), nullable=False, index=True)
    expiration_date = Column(Date, nullable=False, index=True)
    
    # Underlying data
    underlying_price = Column(Numeric(10, 4))
    underlying_change = Column(Numeric(10, 4))
    underlying_change_percent = Column(Float)
    
    # Chain statistics
    total_call_volume = Column(Integer, default=0)
    total_put_volume = Column(Integer, default=0)
    total_call_open_interest = Column(Integer, default=0)
    total_put_open_interest = Column(Integer, default=0)
    put_call_ratio = Column(Float)
    
    # Volatility metrics
    implied_volatility_rank = Column(Float)
    implied_volatility_percentile = Column(Float)
    historical_volatility = Column(Float)
    
    # Market sentiment
    max_pain = Column(Numeric(10, 4))  # Price with maximum option pain
    gamma_exposure = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    contracts = relationship("OptionContract", back_populates="chain")
    
    def calculate_put_call_ratio(self) -> float:
        """Calculate put/call ratio."""
        if self.total_call_volume == 0:
            return 0.0
        return self.total_put_volume / self.total_call_volume


class VolatilitySurface(Base):
    """Volatility surface data model."""
    
    __tablename__ = "volatility_surfaces"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Surface identification
    symbol = Column(String(20), nullable=False, index=True)
    calculation_date = Column(Date, nullable=False, index=True)
    
    # Surface data (stored as JSON)
    surface_data = Column(JSON)  # {strike: {dte: iv}}
    
    # Surface metrics
    atm_volatility = Column(Float)  # At-the-money volatility
    volatility_skew = Column(Float)  # 25-delta put vs call skew
    term_structure_slope = Column(Float)  # Front month vs back month
    
    # Risk metrics
    vega_weighted_iv = Column(Float)
    gamma_weighted_iv = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Pydantic models for API
class OptionContractBase(BaseModel):
    """Base option contract model."""
    symbol: str = Field(..., min_length=1, max_length=20)
    option_type: str = Field(..., pattern="^(call|put)$")
    strike_price: condecimal(gt=0, decimal_places=4)
    expiration_date: date
    
    @validator('expiration_date')
    def validate_expiration_date(cls, v):
        """Validate expiration date is in the future."""
        if v <= date.today():
            raise ValueError('Expiration date must be in the future')
        return v


class OptionContractResponse(OptionContractBase):
    """Option contract response model."""
    id: uuid.UUID
    option_symbol: str
    days_to_expiration: Optional[int] = None
    contract_size: int = 100
    style: str = "american"
    exchange: Optional[str] = None
    
    # Pricing
    bid: Optional[Decimal] = None
    ask: Optional[Decimal] = None
    last_price: Optional[Decimal] = None
    mark_price: Optional[Decimal] = None
    
    # Volume and interest
    volume: int = 0
    open_interest: int = 0
    
    # Greeks
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    rho: Optional[float] = None
    
    # Volatility
    implied_volatility: Optional[float] = None
    historical_volatility: Optional[float] = None
    
    # Risk metrics
    intrinsic_value: Optional[Decimal] = None
    time_value: Optional[Decimal] = None
    moneyness: Optional[float] = None
    
    # Timestamps
    last_trade_time: Optional[datetime] = None
    quote_time: Optional[datetime] = None
    updated_at: datetime
    
    class Config:
        from_attributes = True


class OptionChainRequest(BaseModel):
    """Option chain request model."""
    symbol: str = Field(..., min_length=1, max_length=20)
    expiration_date: Optional[date] = None
    min_days_to_expiration: Optional[int] = Field(None, ge=0)
    max_days_to_expiration: Optional[int] = Field(None, ge=0)
    min_strike: Optional[condecimal(gt=0, decimal_places=4)] = None
    max_strike: Optional[condecimal(gt=0, decimal_places=4)] = None
    option_type: Optional[str] = Field(None, pattern="^(call|put|both)$")
    min_volume: Optional[int] = Field(None, ge=0)
    min_open_interest: Optional[int] = Field(None, ge=0)
    
    @validator('max_days_to_expiration')
    def validate_dte_range(cls, v, values):
        """Validate days to expiration range."""
        min_dte = values.get('min_days_to_expiration')
        if min_dte is not None and v is not None and v < min_dte:
            raise ValueError('max_days_to_expiration must be >= min_days_to_expiration')
        return v
    
    @validator('max_strike')
    def validate_strike_range(cls, v, values):
        """Validate strike price range."""
        min_strike = values.get('min_strike')
        if min_strike is not None and v is not None and v < min_strike:
            raise ValueError('max_strike must be >= min_strike')
        return v


class OptionChainResponse(BaseModel):
    """Option chain response model."""
    id: uuid.UUID
    symbol: str
    expiration_date: date
    underlying_price: Optional[Decimal] = None
    underlying_change: Optional[Decimal] = None
    underlying_change_percent: Optional[float] = None
    
    # Chain statistics
    total_call_volume: int = 0
    total_put_volume: int = 0
    total_call_open_interest: int = 0
    total_put_open_interest: int = 0
    put_call_ratio: Optional[float] = None
    
    # Volatility metrics
    implied_volatility_rank: Optional[float] = None
    implied_volatility_percentile: Optional[float] = None
    historical_volatility: Optional[float] = None
    
    # Market sentiment
    max_pain: Optional[Decimal] = None
    gamma_exposure: Optional[float] = None
    
    # Contracts
    calls: List[OptionContractResponse] = []
    puts: List[OptionContractResponse] = []
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class GreeksCalculationRequest(BaseModel):
    """Greeks calculation request model."""
    symbol: str = Field(..., min_length=1, max_length=20)
    option_type: str = Field(..., pattern="^(call|put)$")
    strike_price: condecimal(gt=0, decimal_places=4)
    expiration_date: date
    underlying_price: condecimal(gt=0, decimal_places=4)
    risk_free_rate: float = Field(default=0.05, ge=0, le=1)
    dividend_yield: float = Field(default=0.0, ge=0, le=1)
    volatility: Optional[float] = Field(None, gt=0, le=5)  # If not provided, will use implied vol


class GreeksResponse(BaseModel):
    """Greeks calculation response model."""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    theoretical_price: Decimal
    intrinsic_value: Decimal
    time_value: Decimal
    implied_volatility: Optional[float] = None
    
    # Additional metrics
    probability_of_profit: Optional[float] = None
    break_even_price: Optional[Decimal] = None
    max_profit: Optional[Decimal] = None
    max_loss: Optional[Decimal] = None


class VolatilitySurfaceResponse(BaseModel):
    """Volatility surface response model."""
    id: uuid.UUID
    symbol: str
    calculation_date: date
    surface_data: Dict[str, Dict[str, float]]  # {strike: {dte: iv}}
    atm_volatility: Optional[float] = None
    volatility_skew: Optional[float] = None
    term_structure_slope: Optional[float] = None
    vega_weighted_iv: Optional[float] = None
    gamma_weighted_iv: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
