"""
Analytics API Routes for QuantEdgeFlow
RESTful endpoints for portfolio analytics, risk metrics, and performance analysis
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, date, timedelta
import uuid

from services.analytics_service import AnalyticsService
from api.middleware.auth import get_current_user, require_permission, Permission
from utils.helpers import validate_symbol


logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/portfolio/{portfolio_id}/performance", response_model=Dict[str, Any])
async def get_portfolio_performance(
    portfolio_id: uuid.UUID = Path(..., description="Portfolio ID"),
    start_date: Optional[date] = Query(None, description="Start date for analysis"),
    end_date: Optional[date] = Query(None, description="End date for analysis"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    analytics_service: AnalyticsService = Depends()
):
    """Get comprehensive portfolio performance analysis."""
    try:
        performance = await analytics_service.calculate_portfolio_performance(
            portfolio_id=portfolio_id,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"Portfolio performance calculated", extra={
            "user_id": user_id,
            "portfolio_id": str(portfolio_id),
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None
        })
        
        return performance
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to calculate portfolio performance: {e}", extra={
            "user_id": user_id,
            "portfolio_id": str(portfolio_id)
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/portfolio/{portfolio_id}/risk", response_model=Dict[str, Any])
async def get_portfolio_risk_metrics(
    portfolio_id: uuid.UUID = Path(..., description="Portfolio ID"),
    confidence_level: float = Query(0.95, ge=0.9, le=0.99, description="Confidence level for VaR calculation"),
    time_horizon: int = Query(1, ge=1, le=30, description="Time horizon in days"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    analytics_service: AnalyticsService = Depends()
):
    """Get portfolio risk metrics including VaR and CVaR."""
    try:
        risk_metrics = await analytics_service.calculate_var_and_cvar(
            portfolio_id=portfolio_id,
            confidence_level=confidence_level,
            time_horizon=time_horizon
        )
        
        logger.info(f"Portfolio risk metrics calculated", extra={
            "user_id": user_id,
            "portfolio_id": str(portfolio_id),
            "confidence_level": confidence_level,
            "time_horizon": time_horizon
        })
        
        return risk_metrics
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to calculate portfolio risk metrics: {e}", extra={
            "user_id": user_id,
            "portfolio_id": str(portfolio_id)
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/signals", response_model=List[Dict[str, Any]])
async def get_trading_signals(
    symbols: List[str] = Query(..., description="List of symbols to analyze"),
    strategy_type: str = Query("momentum", regex="^(momentum|mean_reversion|breakout)$", description="Strategy type"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    analytics_service: AnalyticsService = Depends()
):
    """Generate trading signals for specified symbols."""
    try:
        # Validate symbols
        for symbol in symbols:
            validate_symbol(symbol)
        
        # Limit number of symbols
        if len(symbols) > 50:
            raise HTTPException(
                status_code=400,
                detail="Maximum 50 symbols allowed per request"
            )
        
        signals = await analytics_service.generate_trade_signals(
            symbols=symbols,
            strategy_type=strategy_type
        )
        
        logger.info(f"Trading signals generated", extra={
            "user_id": user_id,
            "symbols_count": len(symbols),
            "strategy_type": strategy_type,
            "signals_count": len(signals)
        })
        
        return signals
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to generate trading signals: {e}", extra={
            "user_id": user_id,
            "symbols": symbols,
            "strategy_type": strategy_type
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/market-overview", response_model=Dict[str, Any])
async def get_market_overview(
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    analytics_service: AnalyticsService = Depends()
):
    """Get market overview and sentiment analysis."""
    try:
        # This would implement comprehensive market analysis
        # For now, return a structured placeholder
        overview = {
            "market_status": "open",
            "major_indices": {
                "SPY": {"price": 450.25, "change": 2.15, "change_pct": 0.48},
                "QQQ": {"price": 375.80, "change": -1.25, "change_pct": -0.33},
                "IWM": {"price": 195.60, "change": 0.85, "change_pct": 0.44}
            },
            "market_sentiment": {
                "vix": 18.5,
                "put_call_ratio": 0.85,
                "advance_decline": 1.2,
                "sentiment_score": "neutral"
            },
            "sector_performance": {
                "Technology": 0.8,
                "Healthcare": 0.3,
                "Finance": -0.2,
                "Energy": 1.5,
                "Consumer": 0.1
            },
            "options_flow": {
                "total_volume": 25000000,
                "call_volume": 15000000,
                "put_volume": 10000000,
                "unusual_activity_count": 15
            },
            "economic_calendar": [
                {
                    "time": "08:30",
                    "event": "Initial Jobless Claims",
                    "importance": "medium",
                    "forecast": "220K",
                    "previous": "215K"
                }
            ],
            "updated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Market overview retrieved", extra={
            "user_id": user_id
        })
        
        return overview
        
    except Exception as e:
        logger.error(f"Failed to get market overview: {e}", extra={
            "user_id": user_id
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/screener", response_model=List[Dict[str, Any]])
async def run_stock_screener(
    min_price: Optional[float] = Query(None, ge=0, description="Minimum stock price"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum stock price"),
    min_volume: Optional[int] = Query(None, ge=0, description="Minimum daily volume"),
    min_market_cap: Optional[float] = Query(None, ge=0, description="Minimum market cap (millions)"),
    max_market_cap: Optional[float] = Query(None, ge=0, description="Maximum market cap (millions)"),
    sector: Optional[str] = Query(None, description="Sector filter"),
    min_rsi: Optional[float] = Query(None, ge=0, le=100, description="Minimum RSI"),
    max_rsi: Optional[float] = Query(None, ge=0, le=100, description="Maximum RSI"),
    limit: int = Query(50, ge=1, le=200, description="Maximum results to return"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    analytics_service: AnalyticsService = Depends()
):
    """Run stock screener with specified criteria."""
    try:
        # Validate price range
        if min_price is not None and max_price is not None and min_price > max_price:
            raise HTTPException(
                status_code=400,
                detail="min_price cannot be greater than max_price"
            )
        
        # Validate market cap range
        if min_market_cap is not None and max_market_cap is not None and min_market_cap > max_market_cap:
            raise HTTPException(
                status_code=400,
                detail="min_market_cap cannot be greater than max_market_cap"
            )
        
        # Validate RSI range
        if min_rsi is not None and max_rsi is not None and min_rsi > max_rsi:
            raise HTTPException(
                status_code=400,
                detail="min_rsi cannot be greater than max_rsi"
            )
        
        # This would implement actual stock screening logic
        # For now, return sample results
        screener_results = [
            {
                "symbol": "AAPL",
                "company_name": "Apple Inc.",
                "price": 175.25,
                "change_pct": 1.2,
                "volume": 45000000,
                "market_cap": 2800000,
                "sector": "Technology",
                "rsi": 65.5,
                "pe_ratio": 28.5,
                "dividend_yield": 0.5,
                "beta": 1.2,
                "score": 85.5
            },
            {
                "symbol": "MSFT",
                "company_name": "Microsoft Corporation",
                "price": 335.80,
                "change_pct": 0.8,
                "volume": 25000000,
                "market_cap": 2500000,
                "sector": "Technology",
                "rsi": 58.2,
                "pe_ratio": 32.1,
                "dividend_yield": 0.7,
                "beta": 0.9,
                "score": 82.3
            }
        ]
        
        # Apply filters (simplified)
        filtered_results = []
        for result in screener_results:
            if min_price is not None and result["price"] < min_price:
                continue
            if max_price is not None and result["price"] > max_price:
                continue
            if min_volume is not None and result["volume"] < min_volume:
                continue
            if min_market_cap is not None and result["market_cap"] < min_market_cap:
                continue
            if max_market_cap is not None and result["market_cap"] > max_market_cap:
                continue
            if sector is not None and result["sector"].lower() != sector.lower():
                continue
            if min_rsi is not None and result["rsi"] < min_rsi:
                continue
            if max_rsi is not None and result["rsi"] > max_rsi:
                continue
            
            filtered_results.append(result)
        
        # Apply limit
        filtered_results = filtered_results[:limit]
        
        logger.info(f"Stock screener executed", extra={
            "user_id": user_id,
            "results_count": len(filtered_results),
            "filters_applied": {
                "min_price": min_price,
                "max_price": max_price,
                "min_volume": min_volume,
                "sector": sector
            }
        })
        
        return filtered_results
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to run stock screener: {e}", extra={
            "user_id": user_id
        })
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/correlation-matrix", response_model=Dict[str, Any])
async def get_correlation_matrix(
    symbols: List[str] = Query(..., description="List of symbols for correlation analysis"),
    period_days: int = Query(252, ge=30, le=1000, description="Period in days for correlation calculation"),
    user_id: str = Depends(require_permission(Permission.READ_ANALYTICS)),
    analytics_service: AnalyticsService = Depends()
):
    """Calculate correlation matrix for specified symbols."""
    try:
        # Validate symbols
        for symbol in symbols:
            validate_symbol(symbol)
        
        # Limit number of symbols
        if len(symbols) > 20:
            raise HTTPException(
                status_code=400,
                detail="Maximum 20 symbols allowed for correlation analysis"
            )
        
        # This would implement actual correlation calculation
        # For now, return sample correlation matrix
        correlation_matrix = {}
        for i, symbol1 in enumerate(symbols):
            correlation_matrix[symbol1] = {}
            for j, symbol2 in enumerate(symbols):
                if i == j:
                    correlation_matrix[symbol1][symbol2] = 1.0
                else:
                    # Sample correlation values
                    correlation_matrix[symbol1][symbol2] = 0.5 + (i - j) * 0.1
        
        result = {
            "symbols": symbols,
            "correlation_matrix": correlation_matrix,
            "period_days": period_days,
            "calculation_date": date.today().isoformat(),
            "statistics": {
                "avg_correlation": 0.65,
                "max_correlation": 0.95,
                "min_correlation": 0.15,
                "highly_correlated_pairs": [
                    {"symbol1": "AAPL", "symbol2": "MSFT", "correlation": 0.85},
                    {"symbol1": "GOOGL", "symbol2": "META", "correlation": 0.78}
                ]
            }
        }
        
        logger.info(f"Correlation matrix calculated", extra={
            "user_id": user_id,
            "symbols_count": len(symbols),
            "period_days": period_days
        })
        
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to calculate correlation matrix: {e}", extra={
            "user_id": user_id,
            "symbols": symbols
        })
        raise HTTPException(status_code=500, detail="Internal server error")
