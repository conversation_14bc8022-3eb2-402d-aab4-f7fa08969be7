#!/usr/bin/env python3
"""
QuantEdgeFlow Startup Script
Comprehensive application startup with health checks and initialization
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config.settings import get_settings
from config.database import init_database, get_async_session
from config.logging_config import setup_logging
from data.storage.cache import cache_manager
from services.background_tasks import start_background_tasks

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

settings = get_settings()


async def check_database_connection() -> bool:
    """Check database connectivity."""
    try:
        async with get_async_session() as session:
            await session.execute("SELECT 1")
        logger.info("✅ Database connection successful")
        return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False


async def check_redis_connection() -> bool:
    """Check Redis connectivity."""
    try:
        await cache_manager.initialize()
        await cache_manager.set("health_check", "ok", ttl=60)
        result = await cache_manager.get("health_check")
        if result == "ok":
            logger.info("✅ Redis connection successful")
            return True
        else:
            logger.error("❌ Redis connection test failed")
            return False
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        return False


async def initialize_database() -> bool:
    """Initialize database tables."""
    try:
        await init_database()
        logger.info("✅ Database initialization successful")
        return True
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False


async def run_health_checks() -> Dict[str, bool]:
    """Run comprehensive health checks."""
    logger.info("🔍 Running health checks...")
    
    health_status = {
        "database": await check_database_connection(),
        "redis": await check_redis_connection(),
    }
    
    # Check API keys
    api_keys_status = {
        "alpha_vantage": bool(settings.ALPHA_VANTAGE_API_KEY),
        "polygon": bool(settings.POLYGON_API_KEY),
    }
    
    health_status["api_keys"] = all(api_keys_status.values())
    
    if health_status["api_keys"]:
        logger.info("✅ API keys configured")
    else:
        logger.warning(f"⚠️ Missing API keys: {[k for k, v in api_keys_status.items() if not v]}")
    
    return health_status


async def start_services() -> bool:
    """Start background services."""
    try:
        logger.info("🚀 Starting background services...")
        
        # Start background tasks
        await start_background_tasks()
        
        logger.info("✅ Background services started")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to start background services: {e}")
        return False


async def main():
    """Main startup routine."""
    logger.info("🎯 Starting QuantEdgeFlow...")
    logger.info(f"📊 Environment: {'Development' if settings.DEBUG else 'Production'}")
    logger.info(f"🌐 Host: {settings.HOST}:{settings.PORT}")
    
    # Run health checks
    health_status = await run_health_checks()
    
    # Check critical services
    critical_services = ["database", "redis"]
    failed_services = [service for service in critical_services if not health_status.get(service, False)]
    
    if failed_services:
        logger.error(f"❌ Critical services failed: {failed_services}")
        logger.error("🛑 Cannot start application")
        sys.exit(1)
    
    # Initialize database
    if not await initialize_database():
        logger.error("🛑 Database initialization failed")
        sys.exit(1)
    
    # Start background services
    if not await start_services():
        logger.warning("⚠️ Some background services failed to start")
    
    logger.info("✅ QuantEdgeFlow startup completed successfully!")
    logger.info("🎉 Application is ready to serve requests")
    
    # Print startup summary
    print("\n" + "="*60)
    print("🎯 QuantEdgeFlow - Options Trading Analytics Platform")
    print("="*60)
    print(f"📊 Environment: {'Development' if settings.DEBUG else 'Production'}")
    print(f"🌐 API Server: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API Docs: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔍 Health Check: http://{settings.HOST}:{settings.PORT}/health")
    print("="*60)
    print("✅ Application is ready!")
    print("="*60 + "\n")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Startup interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Startup failed: {e}")
        sys.exit(1)
