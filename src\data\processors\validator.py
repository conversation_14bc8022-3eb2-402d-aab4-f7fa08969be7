"""
Data Validator for QuantEdgeFlow
Comprehensive data validation and quality assessment
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from dataclasses import dataclass
import warnings

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Data validation result."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    quality_score: float
    metrics: Dict[str, Any]


class DataValidator:
    """Comprehensive data validation engine."""
    
    def __init__(self):
        self.logger = logger
        
        # Validation thresholds
        self.thresholds = {
            'min_data_points': 10,
            'max_missing_ratio': 0.3,
            'max_outlier_ratio': 0.1,
            'min_quality_score': 0.7,
            'max_price_change': 0.5,
            'min_volume': 0,
            'max_spread_ratio': 0.1
        }
    
    def validate_market_data(self, data: pd.DataFrame) -> ValidationResult:
        """Validate market data quality."""
        try:
            errors = []
            warnings = []
            metrics = {}
            
            # Basic structure validation
            structure_valid, structure_errors = self._validate_structure(data, ['close'])
            errors.extend(structure_errors)
            
            if not structure_valid:
                return ValidationResult(False, errors, warnings, 0.0, metrics)
            
            # Data completeness
            completeness_score, completeness_warnings = self._validate_completeness(data)
            warnings.extend(completeness_warnings)
            metrics['completeness_score'] = completeness_score
            
            # Price validation
            price_valid, price_errors, price_warnings = self._validate_prices(data)
            errors.extend(price_errors)
            warnings.extend(price_warnings)
            metrics['price_validation'] = price_valid
            
            # Volume validation
            if 'volume' in data.columns:
                volume_valid, volume_warnings = self._validate_volume(data)
                warnings.extend(volume_warnings)
                metrics['volume_validation'] = volume_valid
            
            # Outlier detection
            outlier_ratio, outlier_warnings = self._detect_outliers(data)
            warnings.extend(outlier_warnings)
            metrics['outlier_ratio'] = outlier_ratio
            
            # Calculate overall quality score
            quality_score = self._calculate_quality_score(metrics)
            
            is_valid = len(errors) == 0 and quality_score >= self.thresholds['min_quality_score']
            
            return ValidationResult(is_valid, errors, warnings, quality_score, metrics)
            
        except Exception as e:
            self.logger.error(f"Market data validation failed: {e}")
            return ValidationResult(False, [str(e)], [], 0.0, {})
    
    def validate_options_data(self, data: pd.DataFrame) -> ValidationResult:
        """Validate options data quality."""
        try:
            errors = []
            warnings = []
            metrics = {}
            
            required_columns = ['strike', 'expiration_date', 'option_type']
            structure_valid, structure_errors = self._validate_structure(data, required_columns)
            errors.extend(structure_errors)
            
            if not structure_valid:
                return ValidationResult(False, errors, warnings, 0.0, metrics)
            
            # Options-specific validations
            options_valid, options_errors, options_warnings = self._validate_options_specific(data)
            errors.extend(options_errors)
            warnings.extend(options_warnings)
            
            # Greeks validation
            if any(col in data.columns for col in ['delta', 'gamma', 'theta', 'vega']):
                greeks_valid, greeks_warnings = self._validate_greeks(data)
                warnings.extend(greeks_warnings)
                metrics['greeks_validation'] = greeks_valid
            
            # Implied volatility validation
            if 'implied_volatility' in data.columns:
                iv_valid, iv_warnings = self._validate_implied_volatility(data)
                warnings.extend(iv_warnings)
                metrics['iv_validation'] = iv_valid
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(metrics)
            
            is_valid = len(errors) == 0 and quality_score >= self.thresholds['min_quality_score']
            
            return ValidationResult(is_valid, errors, warnings, quality_score, metrics)
            
        except Exception as e:
            self.logger.error(f"Options data validation failed: {e}")
            return ValidationResult(False, [str(e)], [], 0.0, {})
    
    def validate_fundamental_data(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate fundamental data quality."""
        try:
            errors = []
            warnings = []
            metrics = {}
            
            if not data:
                errors.append("Fundamental data is empty")
                return ValidationResult(False, errors, warnings, 0.0, metrics)
            
            # Validate numeric fields
            numeric_valid, numeric_warnings = self._validate_fundamental_numeric(data)
            warnings.extend(numeric_warnings)
            metrics['numeric_validation'] = numeric_valid
            
            # Validate ratios
            ratios_valid, ratios_warnings = self._validate_fundamental_ratios(data)
            warnings.extend(ratios_warnings)
            metrics['ratios_validation'] = ratios_valid
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(metrics)
            
            is_valid = len(errors) == 0 and quality_score >= self.thresholds['min_quality_score']
            
            return ValidationResult(is_valid, errors, warnings, quality_score, metrics)
            
        except Exception as e:
            self.logger.error(f"Fundamental data validation failed: {e}")
            return ValidationResult(False, [str(e)], [], 0.0, {})
    
    def _validate_structure(self, data: pd.DataFrame, required_columns: List[str]) -> Tuple[bool, List[str]]:
        """Validate data structure."""
        errors = []
        
        # Check if data is empty
        if data.empty:
            errors.append("Data is empty")
            return False, errors
        
        # Check minimum data points
        if len(data) < self.thresholds['min_data_points']:
            errors.append(f"Insufficient data points: {len(data)} < {self.thresholds['min_data_points']}")
        
        # Check required columns
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {missing_columns}")
        
        return len(errors) == 0, errors
    
    def _validate_completeness(self, data: pd.DataFrame) -> Tuple[float, List[str]]:
        """Validate data completeness."""
        warnings = []
        
        # Calculate missing data ratio
        missing_ratio = data.isnull().sum().sum() / (len(data) * len(data.columns))
        
        if missing_ratio > self.thresholds['max_missing_ratio']:
            warnings.append(f"High missing data ratio: {missing_ratio:.2%}")
        
        completeness_score = 1.0 - missing_ratio
        return completeness_score, warnings
    
    def _validate_prices(self, data: pd.DataFrame) -> Tuple[bool, List[str], List[str]]:
        """Validate price data."""
        errors = []
        warnings = []
        
        # Check for negative prices
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                negative_prices = (data[col] <= 0).sum()
                if negative_prices > 0:
                    errors.append(f"Found {negative_prices} negative/zero prices in {col}")
        
        # Check OHLC relationships
        if all(col in data.columns for col in price_columns):
            invalid_high = (data['high'] < np.maximum(data['open'], data['close'])).sum()
            invalid_low = (data['low'] > np.minimum(data['open'], data['close'])).sum()
            
            if invalid_high > 0:
                warnings.append(f"Found {invalid_high} invalid high prices")
            if invalid_low > 0:
                warnings.append(f"Found {invalid_low} invalid low prices")
        
        # Check for extreme price changes
        if 'close' in data.columns:
            returns = data['close'].pct_change().abs()
            extreme_changes = (returns > self.thresholds['max_price_change']).sum()
            if extreme_changes > 0:
                warnings.append(f"Found {extreme_changes} extreme price changes")
        
        return len(errors) == 0, errors, warnings
    
    def _validate_volume(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate volume data."""
        warnings = []
        
        # Check for negative volume
        negative_volume = (data['volume'] < 0).sum()
        if negative_volume > 0:
            warnings.append(f"Found {negative_volume} negative volume values")
        
        # Check for zero volume
        zero_volume = (data['volume'] == 0).sum()
        if zero_volume > len(data) * 0.1:  # More than 10% zero volume
            warnings.append(f"High proportion of zero volume: {zero_volume}/{len(data)}")
        
        return True, warnings
    
    def _detect_outliers(self, data: pd.DataFrame) -> Tuple[float, List[str]]:
        """Detect outliers in the data."""
        warnings = []
        
        if 'close' not in data.columns:
            return 0.0, warnings
        
        # Calculate returns
        returns = data['close'].pct_change().dropna()
        
        if len(returns) == 0:
            return 0.0, warnings
        
        # Detect outliers using z-score
        z_scores = np.abs((returns - returns.mean()) / returns.std())
        outliers = (z_scores > 3).sum()
        outlier_ratio = outliers / len(returns)
        
        if outlier_ratio > self.thresholds['max_outlier_ratio']:
            warnings.append(f"High outlier ratio: {outlier_ratio:.2%}")
        
        return outlier_ratio, warnings
    
    def _validate_options_specific(self, data: pd.DataFrame) -> Tuple[bool, List[str], List[str]]:
        """Validate options-specific data."""
        errors = []
        warnings = []
        
        # Check strike prices
        if 'strike' in data.columns:
            invalid_strikes = (data['strike'] <= 0).sum()
            if invalid_strikes > 0:
                errors.append(f"Found {invalid_strikes} invalid strike prices")
        
        # Check option types
        if 'option_type' in data.columns:
            valid_types = ['call', 'put', 'Call', 'Put', 'CALL', 'PUT']
            invalid_types = (~data['option_type'].isin(valid_types)).sum()
            if invalid_types > 0:
                errors.append(f"Found {invalid_types} invalid option types")
        
        # Check expiration dates
        if 'expiration_date' in data.columns:
            current_date = datetime.now().date()
            expired_options = (pd.to_datetime(data['expiration_date']).dt.date < current_date).sum()
            if expired_options > 0:
                warnings.append(f"Found {expired_options} expired options")
        
        # Check bid-ask spread
        if 'bid' in data.columns and 'ask' in data.columns:
            invalid_spreads = (data['ask'] < data['bid']).sum()
            if invalid_spreads > 0:
                errors.append(f"Found {invalid_spreads} invalid bid-ask spreads")
            
            # Check for wide spreads
            mid_price = (data['bid'] + data['ask']) / 2
            spread_ratio = (data['ask'] - data['bid']) / mid_price
            wide_spreads = (spread_ratio > self.thresholds['max_spread_ratio']).sum()
            if wide_spreads > 0:
                warnings.append(f"Found {wide_spreads} wide bid-ask spreads")
        
        return len(errors) == 0, errors, warnings
    
    def _validate_greeks(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate options Greeks."""
        warnings = []
        
        # Delta validation
        if 'delta' in data.columns:
            invalid_delta = ((data['delta'] < -1) | (data['delta'] > 1)).sum()
            if invalid_delta > 0:
                warnings.append(f"Found {invalid_delta} invalid delta values")
        
        # Gamma validation
        if 'gamma' in data.columns:
            negative_gamma = (data['gamma'] < 0).sum()
            if negative_gamma > 0:
                warnings.append(f"Found {negative_gamma} negative gamma values")
        
        # Vega validation
        if 'vega' in data.columns:
            negative_vega = (data['vega'] < 0).sum()
            if negative_vega > 0:
                warnings.append(f"Found {negative_vega} negative vega values")
        
        return True, warnings
    
    def _validate_implied_volatility(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate implied volatility."""
        warnings = []
        
        # Check for negative IV
        negative_iv = (data['implied_volatility'] < 0).sum()
        if negative_iv > 0:
            warnings.append(f"Found {negative_iv} negative implied volatility values")
        
        # Check for extremely high IV
        high_iv = (data['implied_volatility'] > 5).sum()  # 500% IV
        if high_iv > 0:
            warnings.append(f"Found {high_iv} extremely high implied volatility values")
        
        return True, warnings
    
    def _validate_fundamental_numeric(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate fundamental numeric fields."""
        warnings = []
        
        numeric_fields = ['pe_ratio', 'price_to_book', 'debt_to_equity', 'current_ratio']
        
        for field in numeric_fields:
            if field in data and data[field] is not None:
                value = data[field]
                if isinstance(value, (int, float)):
                    if field == 'pe_ratio' and (value < 0 or value > 1000):
                        warnings.append(f"Unusual {field}: {value}")
                    elif field == 'current_ratio' and value < 0:
                        warnings.append(f"Negative {field}: {value}")
        
        return True, warnings
    
    def _validate_fundamental_ratios(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate fundamental ratios for consistency."""
        warnings = []
        
        # Add ratio consistency checks here
        # For example, check if margins are within reasonable ranges
        
        return True, warnings
    
    def _calculate_quality_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall data quality score."""
        try:
            scores = []
            
            # Completeness score
            if 'completeness_score' in metrics:
                scores.append(metrics['completeness_score'])
            
            # Validation scores
            validation_fields = ['price_validation', 'volume_validation', 'greeks_validation', 'iv_validation']
            for field in validation_fields:
                if field in metrics:
                    scores.append(1.0 if metrics[field] else 0.0)
            
            # Outlier penalty
            if 'outlier_ratio' in metrics:
                outlier_penalty = min(1.0, metrics['outlier_ratio'] * 10)  # Penalty for outliers
                scores.append(1.0 - outlier_penalty)
            
            # Calculate weighted average
            if scores:
                return sum(scores) / len(scores)
            else:
                return 0.5  # Default score
                
        except Exception as e:
            self.logger.error(f"Quality score calculation failed: {e}")
            return 0.0


# Global data validator instance
data_validator = DataValidator()
