"""
Portfolio Service for QuantEdgeFlow
Business logic for portfolio management and operations
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession

from src.config.database import get_async_session
from src.core.models.portfolio import (
    Portfolio, PortfolioPosition, PositionStatus, PositionType,
    PortfolioCreate, PortfolioUpdate, PortfolioResponse, 
    PortfolioPositionCreate, PortfolioPositionResponse, PortfolioMetrics
)
from src.core.models.trades import Trade, TradeStatus
from src.utils.logger import get_trading_logger, log_audit_event


logger = logging.getLogger(__name__)
trading_logger = get_trading_logger()


class PortfolioService:
    """Portfolio management service."""
    
    def __init__(self):
        self.logger = logger
    
    async def create_portfolio(
        self, 
        user_id: str, 
        portfolio_data: PortfolioCreate
    ) -> PortfolioResponse:
        """Create a new portfolio."""
        async with get_async_session() as session:
            try:
                # Create portfolio instance
                portfolio = Portfolio(
                    user_id=user_id,
                    name=portfolio_data.name,
                    description=portfolio_data.description,
                    initial_capital=portfolio_data.initial_capital,
                    cash_balance=portfolio_data.initial_capital,
                    nav=portfolio_data.initial_capital,
                    risk_tolerance=portfolio_data.risk_tolerance,
                    max_position_size=portfolio_data.max_position_size,
                    max_sector_exposure=portfolio_data.max_sector_exposure,
                    is_paper_trading=portfolio_data.is_paper_trading
                )
                
                session.add(portfolio)
                await session.commit()
                await session.refresh(portfolio)
                
                # Log portfolio creation
                log_audit_event(
                    "portfolio_created",
                    user_id,
                    {
                        "portfolio_id": str(portfolio.id),
                        "name": portfolio.name,
                        "initial_capital": float(portfolio.initial_capital),
                        "is_paper_trading": portfolio.is_paper_trading
                    }
                )
                
                self.logger.info(f"Portfolio created successfully", extra={
                    "user_id": user_id,
                    "portfolio_id": str(portfolio.id),
                    "name": portfolio.name
                })
                
                return PortfolioResponse.from_orm(portfolio)
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to create portfolio: {e}", extra={
                    "user_id": user_id,
                    "portfolio_name": portfolio_data.name
                })
                raise
    
    async def get_user_portfolios(self, user_id: str) -> List[PortfolioResponse]:
        """Get all portfolios for a user."""
        async with get_async_session() as session:
            try:
                stmt = select(Portfolio).where(
                    and_(
                        Portfolio.user_id == user_id,
                        Portfolio.is_active == True
                    )
                ).options(selectinload(Portfolio.positions))
                
                result = await session.execute(stmt)
                portfolios = result.scalars().all()
                
                return [PortfolioResponse.from_orm(portfolio) for portfolio in portfolios]
                
            except Exception as e:
                self.logger.error(f"Failed to get user portfolios: {e}", extra={
                    "user_id": user_id
                })
                raise
    
    async def get_portfolio_by_id(
        self, 
        portfolio_id: uuid.UUID, 
        user_id: str
    ) -> Optional[PortfolioResponse]:
        """Get portfolio by ID."""
        async with get_async_session() as session:
            try:
                stmt = select(Portfolio).where(
                    and_(
                        Portfolio.id == portfolio_id,
                        Portfolio.user_id == user_id,
                        Portfolio.is_active == True
                    )
                ).options(selectinload(Portfolio.positions))
                
                result = await session.execute(stmt)
                portfolio = result.scalar_one_or_none()
                
                if portfolio:
                    return PortfolioResponse.from_orm(portfolio)
                return None
                
            except Exception as e:
                self.logger.error(f"Failed to get portfolio: {e}", extra={
                    "portfolio_id": str(portfolio_id),
                    "user_id": user_id
                })
                raise
    
    async def update_portfolio(
        self, 
        portfolio_id: uuid.UUID, 
        user_id: str, 
        update_data: PortfolioUpdate
    ) -> Optional[PortfolioResponse]:
        """Update portfolio."""
        async with get_async_session() as session:
            try:
                # Get existing portfolio
                stmt = select(Portfolio).where(
                    and_(
                        Portfolio.id == portfolio_id,
                        Portfolio.user_id == user_id,
                        Portfolio.is_active == True
                    )
                )
                
                result = await session.execute(stmt)
                portfolio = result.scalar_one_or_none()
                
                if not portfolio:
                    return None
                
                # Update fields
                update_dict = update_data.dict(exclude_unset=True)
                for field, value in update_dict.items():
                    setattr(portfolio, field, value)
                
                portfolio.updated_at = datetime.utcnow()
                
                await session.commit()
                await session.refresh(portfolio)
                
                # Log portfolio update
                log_audit_event(
                    "portfolio_updated",
                    user_id,
                    {
                        "portfolio_id": str(portfolio.id),
                        "updated_fields": list(update_dict.keys())
                    }
                )
                
                return PortfolioResponse.from_orm(portfolio)
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to update portfolio: {e}", extra={
                    "portfolio_id": str(portfolio_id),
                    "user_id": user_id
                })
                raise
    
    async def delete_portfolio(
        self, 
        portfolio_id: uuid.UUID, 
        user_id: str
    ) -> bool:
        """Soft delete portfolio."""
        async with get_async_session() as session:
            try:
                stmt = update(Portfolio).where(
                    and_(
                        Portfolio.id == portfolio_id,
                        Portfolio.user_id == user_id
                    )
                ).values(
                    is_active=False,
                    updated_at=datetime.utcnow()
                )
                
                result = await session.execute(stmt)
                await session.commit()
                
                if result.rowcount > 0:
                    log_audit_event(
                        "portfolio_deleted",
                        user_id,
                        {"portfolio_id": str(portfolio_id)}
                    )
                    return True
                
                return False
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to delete portfolio: {e}", extra={
                    "portfolio_id": str(portfolio_id),
                    "user_id": user_id
                })
                raise
    
    async def add_position(
        self, 
        portfolio_id: uuid.UUID, 
        user_id: str, 
        position_data: PortfolioPositionCreate
    ) -> Optional[PortfolioPositionResponse]:
        """Add position to portfolio."""
        async with get_async_session() as session:
            try:
                # Verify portfolio ownership
                portfolio_stmt = select(Portfolio).where(
                    and_(
                        Portfolio.id == portfolio_id,
                        Portfolio.user_id == user_id,
                        Portfolio.is_active == True
                    )
                )
                
                portfolio_result = await session.execute(portfolio_stmt)
                portfolio = portfolio_result.scalar_one_or_none()
                
                if not portfolio:
                    return None
                
                # Create position
                position = PortfolioPosition(
                    portfolio_id=portfolio_id,
                    symbol=position_data.symbol.upper(),
                    instrument_type=position_data.instrument_type,
                    position_type=position_data.position_type,
                    quantity=position_data.quantity,
                    avg_cost=position_data.avg_cost,
                    option_type=position_data.option_type,
                    strike_price=position_data.strike_price,
                    expiration_date=position_data.expiration_date,
                    status=PositionStatus.OPEN
                )
                
                # Calculate initial market value
                position.current_price = position.avg_cost
                position.market_value = position.avg_cost * abs(position.quantity)
                
                session.add(position)
                
                # Update portfolio cash balance
                total_cost = position.avg_cost * abs(position.quantity)
                if position.position_type == PositionType.LONG:
                    portfolio.cash_balance -= total_cost
                else:  # SHORT position adds cash
                    portfolio.cash_balance += total_cost
                
                await session.commit()
                await session.refresh(position)
                
                # Log position addition
                trading_logger.info(f"Position added to portfolio", extra={
                    "user_id": user_id,
                    "portfolio_id": str(portfolio_id),
                    "position_id": str(position.id),
                    "symbol": position.symbol,
                    "quantity": position.quantity,
                    "avg_cost": float(position.avg_cost)
                })
                
                return PortfolioPositionResponse.from_orm(position)
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Failed to add position: {e}", extra={
                    "portfolio_id": str(portfolio_id),
                    "user_id": user_id,
                    "symbol": position_data.symbol
                })
                raise
    
    async def get_portfolio_positions(
        self, 
        portfolio_id: uuid.UUID, 
        user_id: str,
        status: Optional[str] = None
    ) -> List[PortfolioPositionResponse]:
        """Get portfolio positions."""
        async with get_async_session() as session:
            try:
                # Build query conditions
                conditions = [
                    PortfolioPosition.portfolio_id == portfolio_id,
                    Portfolio.user_id == user_id,
                    Portfolio.is_active == True
                ]
                
                if status:
                    conditions.append(PortfolioPosition.status == status)
                
                stmt = select(PortfolioPosition).join(Portfolio).where(
                    and_(*conditions)
                )
                
                result = await session.execute(stmt)
                positions = result.scalars().all()
                
                return [PortfolioPositionResponse.from_orm(pos) for pos in positions]
                
            except Exception as e:
                self.logger.error(f"Failed to get portfolio positions: {e}", extra={
                    "portfolio_id": str(portfolio_id),
                    "user_id": user_id
                })
                raise
    
    async def calculate_portfolio_metrics(
        self, 
        portfolio: Portfolio
    ) -> PortfolioMetrics:
        """Calculate comprehensive portfolio metrics."""
        try:
            # Calculate current NAV
            current_nav = portfolio.calculate_nav()
            
            # Calculate total return
            total_return = float(current_nav - portfolio.initial_capital)
            total_return_pct = (total_return / float(portfolio.initial_capital)) * 100
            
            # Calculate Greeks
            greeks = portfolio.calculate_total_greeks()
            
            # Calculate basic risk metrics (simplified for now)
            # In production, these would use more sophisticated calculations
            portfolio_beta = 1.0  # Placeholder
            var_1d = None  # Would calculate based on position correlations
            var_5d = None  # Would calculate based on position correlations
            
            return PortfolioMetrics(
                nav=current_nav,
                cash_balance=portfolio.cash_balance,
                total_return=total_return,
                total_return_pct=total_return_pct,
                max_drawdown=portfolio.max_drawdown,
                sharpe_ratio=portfolio.sharpe_ratio,
                win_rate=portfolio.win_rate,
                profit_factor=portfolio.profit_factor,
                total_delta=greeks["delta"],
                total_gamma=greeks["gamma"],
                total_theta=greeks["theta"],
                total_vega=greeks["vega"],
                total_rho=greeks["rho"],
                portfolio_beta=portfolio_beta,
                var_1d=var_1d,
                var_5d=var_5d
            )
            
        except Exception as e:
            self.logger.error(f"Failed to calculate portfolio metrics: {e}")
            raise
